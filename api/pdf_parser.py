import logging
import os
import shutil
import sys
import time
from datetime import datetime
from urllib.parse import unquote, urlparse

import nest_asyncio
import requests
from fastapi import APIRouter, File, Request, UploadFile
from llama_parse import LlamaParse
from llama_parse.utils import Language

from configurer.config_reader import get_lamma_index_config
from models.pdf_parse import PdfParseRequest, PdfParseResponse

logger = logging.getLogger(name=__name__)

curPath = os.path.abspath(os.path.dirname(__file__))
rootPath = os.path.split(curPath)[0]
sys.path.append(rootPath)

# 允许嵌套的事件循环 - 与uvloop冲突，暂时注释
try:
    nest_asyncio.apply()  # 允许嵌套的事件循环
except ValueError as e:
    if "Can't patch loop of type" in str(e):
        # uvloop与nest_asyncio冲突，跳过
        pass
    else:
        raise

router = APIRouter(include_in_schema=False)

# 创建存储文件的文件夹
UPLOAD_FOLDER = "uploads"
if not os.path.exists(UPLOAD_FOLDER):
    os.makedirs(UPLOAD_FOLDER)


def get_pdf_filename_from_url(url):
    """从URL中提取PDF文件名"""
    parsed_url = urlparse(url)  # 解析URL
    path = parsed_url.path  # 获取路径部分
    filename = path.split("/")[-1]  # 提取最后一部分
    return unquote(filename)  # 解码URL编码（如%转义）


def download_file(file_url, save_dir="temp_files"):
    """下载文件到本地临时目录"""
    os.makedirs(save_dir, exist_ok=True)  # 确保临时目录存在
    file_name = os.path.join(save_dir, get_pdf_filename_from_url(file_url))
    response = requests.get(file_url, stream=True)
    if response.status_code == 200:
        with open(file_name, "wb") as file:
            for chunk in response.iter_content(chunk_size=8192):
                file.write(chunk)
        return file_name
    else:
        logger.error(f"Failed to download file from URL: {file_url}")
        raise Exception(f"Failed to download file. HTTP Status: {response.status_code}")


def parse_file_with_llamaparser(file_path):
    """调用 LlamaParser Python SDK 解析文件"""
    config = get_lamma_index_config()

    if config["premium"]:
        documents = LlamaParse(
            result_type="markdown",
            premium_mode=True,
            language=Language.SIMPLIFIED_CHINESE,
            api_key=config["api_key"],
        ).load_data(file_path)
    else:
        documents = LlamaParse(
            result_type="markdown",
            use_vendor_multimodal_model=True,
            vendor_multimodal_model_name="gemini-2.0-flash-001",
            language=Language.SIMPLIFIED_CHINESE,
            api_key=config["api_key"],
        ).load_data(file_path)

    # 将 documents[i].text 属性聚合，并以 \n 分割
    result = "\n".join([doc.text for doc in documents])

    return result


def build_success_result(result, start):
    res = PdfParseResponse(
        code=200,
        message="success",
        result=result,
        success=True,
        time_cost=(time.time() - start) * 1000,
    )
    # return json.dumps(res.__dict__)
    return res


def build_error_result(err, start):
    res = PdfParseResponse(
        code=500,
        message=str(err),
        result=None,
        success=False,
        time_cost=(time.time() - start) * 1000,
    )
    # return json.dumps(res.__dict__)
    return res


@router.post("/parse-pdf")
def parse_pdf(pdf_parse_request: PdfParseRequest):
    start = time.time()

    try:
        # Step 1: 获取文件 URL
        file_url = pdf_parse_request.file_url

        logger.info(f"received request: {file_url}")
        if not file_url:
            return build_error_result("file_url is required", start)

        # Step 2: 下载文件到本地
        file_path = download_file(file_url)

        # Step 3: 调用 LlamaParser SDK 解析文件
        parsed_content = parse_file_with_llamaparser(file_path)

        # 清理临时文件
        os.remove(file_path)

        # Step 4: 返回解析结果
        return build_success_result(parsed_content, start)

    except Exception as e:
        return build_error_result(str(e), start)


@router.post("/parse-pdf2")
def parse_pdf2(file: UploadFile = File(...)):
    start = time.time()

    try:
        file_path = os.path.join(UPLOAD_FOLDER, file.filename)
        # 保存上传的文件
        with open(file_path, "wb") as buffer:
            shutil.copyfileobj(file.file, buffer)

        # Step 3: 调用 LlamaParser SDK 解析文件
        parsed_content = parse_file_with_llamaparser(file_path)

        # 清理临时文件
        os.remove(file_path)

        # Step 4: 返回解析结果
        return build_success_result(parsed_content, start)
    except Exception as e:
        return build_error_result(str(e), start)


@router.post("/parse-pdf3")
async def parse_pdf2(request: Request):
    start = time.time()
    now = datetime.now()
    formatted_time = now.strftime("%Y%m%d%H%M%S")
    filename = f"{formatted_time}.pdf"

    try:
        current_file_path = os.path.dirname(__file__)
        file_path = os.path.join(current_file_path, "..", UPLOAD_FOLDER, filename)

        content = await request.body()

        # 保存上传的文件
        with open(file_path, "wb") as f:
            f.write(content)

        # Step 3: 调用 LlamaParser SDK 解析文件
        parsed_content = parse_file_with_llamaparser(file_path)

        # 清理临时文件
        os.remove(file_path)

        # Step 4: 返回解析结果
        return build_success_result(parsed_content, start)
    except Exception as e:
        return build_error_result(str(e), start)
