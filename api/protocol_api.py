# api/protocol_api.py

from fastapi import APIRouter, Request
import os
from logger.logger import app_logger
from models.protocol_vo import ProtocolFileRequest
from models.result import make_fail, make_success
from utils.oss_utils import bucket

# 全局变量缓存导入的模块，避免重复导入
_celery_modules = None

router = APIRouter(prefix="/protocols", tags=["Protocols"])


def _sniff_office_type_by_header(file_key: str) -> str:
    """
    仅读前 8 字节判断：
    - 返回 'doc' 表示 OLE/CFB（旧版 .doc）
    - 返回 'zip' 表示 ZIP 容器（docx 候选）
    - 其他返回 'unknown'
    """
    try:
        # 阿里云 OSS SDK 支持范围读取
        obj = bucket.get_object(file_key, byte_range=(0, 7))
        head = obj.read(8) or b""
        if len(head) >= 8 and head[:8] == b"\xD0\xCF\x11\xE0\xA1\xB1\x1A\xE1":
            return "doc"
        if len(head) >= 2 and head[:2] == b"PK":
            return "zip"
        return "unknown"
    except Exception as e:
        # 预读失败不致命，按 unknown 处理
        app_logger.warning(f"预读文件头失败: {e}")
        return "unknown"

def _get_celery_modules():
    """获取 Celery 模块，使用缓存避免重复导入"""
    global _celery_modules
    if _celery_modules is None:
        from celery.result import AsyncResult

        from celery_task.celery import celery_app
        from celery_task.protocol_task import extract_protocol_task

        _celery_modules = {
            "AsyncResult": AsyncResult,
            "celery_app": celery_app,
            "extract_protocol_task": extract_protocol_task,
        }
    return _celery_modules


@router.post("/extract-by-docx-async")
async def extract_structure_from_docx_async(request: ProtocolFileRequest, http_request: Request = None):
    """
    异步从OSS上的.docx方案文件中解析出层级化JSON结构。
    - 首先检查OSS文件是否存在
    - 如果文件存在，提交任务到 Celery 队列，立即返回任务 ID
    - 如果文件不存在，直接返回错误，不提交任务
    - 客户端可以通过任务 ID 查询处理状态和结果
    """
    file_key = request.protocol_file.file_key
    extract_keys_only = getattr(request, "extract_keys_only", False)

    try:
        # 记录请求来源信息，特别是测试文件的请求
        client_ip = getattr(http_request.client, "host", "unknown") if http_request else "unknown"
        user_agent = http_request.headers.get("user-agent", "unknown") if http_request else "unknown"

        if "test" in file_key or "non-existent" in file_key:
            app_logger.warning(f"检测到测试文件请求: {file_key}, 来源IP: {client_ip}, User-Agent: {user_agent}")
        else:
            app_logger.info(f"检查协议文件是否存在: {file_key}")

        # 修复：在提交任务前先检查OSS文件是否存在
        try:
            if not bucket.object_exists(file_key):
                error_msg = f"OSS文件不存在: '{file_key}'"
                app_logger.error(error_msg)
                return make_fail(404, error_msg)
        except Exception as oss_e:
            error_msg = f"检查OSS文件时发生错误: {str(oss_e)}"
            app_logger.error(error_msg)
            return make_fail(500, error_msg)

        # 修复：在提交任务前检查是否有相同文件正在处理
        from utils.concurrency_manager import task_throttler

        try:
            # 检查是否有相同文件正在处理（1分钟内）
            if not task_throttler.is_allowed(f"file_processing:{file_key}", limit=1, window_seconds=60):
                error_msg = f"文件正在处理中，请稍后再试: {file_key.split('/')[-1]}"
                app_logger.warning(error_msg)
                return make_fail(429, error_msg)  # 429 Too Many Requests
        except Exception as rate_check_e:
            app_logger.warning(f"限流检查失败，继续处理: {rate_check_e}")

        app_logger.info(f"文件存在且无重复处理，提交协议解析任务: {file_key}, extract_keys_only: {extract_keys_only}")

        # （在限流检查之后，提交任务之前）
        # Fail-Fast 开关（可用环境变量控制，默认开启）

        fail_fast = os.environ.get("FAIL_FAST_DOCX_CHECK", "true").lower() == "true"
        allow_convert = os.environ.get("ENABLE_DOC_CONVERSION", "false").lower() == "true"

        if fail_fast:
            sniff = _sniff_office_type_by_header(file_key)
            if sniff == "doc":
                if not allow_convert:
                    # 直接拦截：旧版 DOC 且未开启自动转换
                    error_msg = "上传的文件是旧版 .doc，请上传正确的 .docx；或启用自动转换后重试"
                    app_logger.warning(f"Fail-Fast 拦截: {file_key} -> {error_msg}")
                    return make_fail(400, error_msg)
                else:
                    app_logger.info(f"检测到 DOC，将在 Celery 中尝试自动转换: {file_key}")
            elif sniff == "unknown":
                # 既不是 ZIP，也不是 DOC（或预读失败）
                error_msg = "上传的文件不是有效的 .docx（检测到非 ZIP 容器）。"
                app_logger.warning(f"Fail-Fast 拦截: {file_key} -> {error_msg}")
                return make_fail(400, error_msg)
            # sniff == "zip" 则继续提交

        # 获取 Celery 模块（带缓存）
        celery_modules = _get_celery_modules()
        extract_protocol_task = celery_modules["extract_protocol_task"]

        # 提交任务到 Celery 队列
        task = extract_protocol_task.delay(file_key, extract_keys_only)
        task_id = task.id

        # 获取当前Pod信息，便于调试和监控
        pod_name = os.environ.get("HOSTNAME", "unknown-pod")

        app_logger.info(f"协议解析任务已提交，Task ID: {task_id}, Pod: {pod_name}")

        response_data = {
            "task_id": task_id,
            "file_key": file_key,
            "extract_keys_only": extract_keys_only,
            "submitted_by_pod": pod_name,  # 添加提交Pod信息
            "message": "协议解析任务已成功提交，正在后台处理中。请使用 task_id 查询处理状态。",
        }

        return make_success(response_data, 0)

    except Exception as e:
        app_logger.error(f"提交协议解析任务失败, Key: '{file_key}'. 错误: {e}", exc_info=True)
        return make_fail(500, f"Failed to submit protocol parsing task: {e}")

@router.get("/task-status/{task_id}")
async def get_protocol_task_status(task_id: str):
    celery_modules = _get_celery_modules()
    AsyncResult = celery_modules["AsyncResult"]
    celery_app = celery_modules["celery_app"]

    try:
        app_logger.info(f"查询协议解析任务状态: {task_id}")
        task_result = AsyncResult(task_id, app=celery_app)

        # 安全读取 state（元数据畸形时可能抛异常）
        try:
            state = task_result.state
        except Exception as e:
            app_logger.error(f"读取任务状态异常（元数据可能损坏）: {e}", exc_info=True)
            response_data = {
                "task_id": task_id,
                "status": "BROKEN",
                "error": str(e),
                "message": "任务状态元数据损坏，建议删除并重试"
            }
            return make_success(response_data, 0)

        # 直接按 state 分支返回（不要先查 redis.exists）
        if state == "PENDING":
            # 任务已提交但尚未开始/未写结果
            response_data = {"task_id": task_id, "status": "PENDING", "message": "任务正在等待执行"}
        elif state == "PROGRESS":
            response_data = {
                "task_id": task_id,
                "status": "PROGRESS",
                "progress_info": task_result.info,
                "message": "任务正在执行中",
            }
        elif state == "SUCCESS":
            if task_result.result is not None:
                response_data = task_result.result
            else:
                app_logger.error(f"任务 {task_id} 状态为SUCCESS但结果为null")
                response_data = {
                    "task_id": task_id,
                    "status": "FAILURE",
                    "error": "任务执行完成但未返回结果，可能执行过程中出现异常",
                    "message": "任务执行失败",
                }
        elif state == "FAILURE":
            response_data = {
                "task_id": task_id,
                "status": "FAILURE",
                "error": str(task_result.info),
                "message": "任务执行失败",
            }
        else:
            response_data = {
                "task_id": task_id,
                "status": state,
                "info": task_result.info,
                "message": f"任务状态: {state}",
            }

        return make_success(response_data, 0)

    except Exception as e:
        app_logger.error(f"查询任务状态失败, Task ID: '{task_id}'. 错误: {e}", exc_info=True)
        return make_fail(500, f"Failed to get task status: {e}")


# 保留原有的同步接口，用于向后兼容
@router.post("/extract-by-docx")
async def extract_structure_from_docx(request: ProtocolFileRequest):
    """
    从OSS上的.docx方案文件中解析出层级化JSON结构（同步版本，保持向后兼容）。
    推荐使用异步版本 /extract-by-docx-async
    """
    # 重定向到异步版本
    return await extract_structure_from_docx_async(request)
