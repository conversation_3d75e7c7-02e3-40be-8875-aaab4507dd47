import logging
import os
import sys
import time

import nest_asyncio
from fastapi import APIRouter

from logger.logger import app_logger
from models.file_diff_request import DocDiffConvertRequest, FileDiffRequest, SingleFileDiffRequest
from models.result import make_fail, make_success
from utils.docx_utils import (
    compare_docx_files,
    generate_change_report_optimized,
    get_doc_diff_data_v2,
    get_doc_diff_data_v3,
    parse_icf_docx,
)
from utils.oss_utils import download_file

logger = logging.getLogger(name=__name__)

curPath = os.path.abspath(os.path.dirname(__file__))
rootPath = os.path.split(curPath)[0]
sys.path.append(rootPath)

# 允许嵌套的事件循环 - 与uvloop冲突，暂时注释
try:
    nest_asyncio.apply()  # 允许嵌套的事件循环
except ValueError as e:
    if "Can't patch loop of type" in str(e):
        # uvloop与nest_asyncio冲突，跳过
        pass
    else:
        raise

router = APIRouter(include_in_schema=False)

# 创建存储文件的文件夹
UPLOAD_FOLDER = "uploads"
if not os.path.exists(UPLOAD_FOLDER):
    os.makedirs(UPLOAD_FOLDER)


@router.post("/doc/diff")
async def compair_doc_diff(request: FileDiffRequest):
    start = time.time()

    try:
        # 参数检查
        if not request.original_file or not request.revised_file:
            return make_fail(400, "original_file and revised_file are required.")

        # OSS 文件下载
        original_file_path = os.path.join(UPLOAD_FOLDER, request.original_file.file_name)
        revised_file_path = os.path.join(UPLOAD_FOLDER, request.revised_file.file_name)
        download_file(request.original_file.file_key, original_file_path)
        download_file(request.revised_file.file_key, revised_file_path)

        # 比较文件差异
        changes = compare_docx_files(original_file_path, revised_file_path)

        # 返回
        # os.remove(original_file_path)
        # os.remove(revised_file_path)

        # Step 4: 返回解析结果
        end = time.time()
        return make_success(changes, int(end - start))
    except Exception as e:
        return make_fail(500, str(e))


@router.post("/doc/single_diff/v3")
async def compair_doc_diff(request: SingleFileDiffRequest):
    start = time.time()
    logger.info(f"开始处理文档差异分析请求: {request.file.file_name}")

    try:
        # 参数检查
        if not request.file:
            logger.error("请求参数错误: 缺少文件参数")
            return make_fail(400, "file are required.")

        # OSS 文件下载
        download_start = time.time()
        revised_file_path = os.path.join(UPLOAD_FOLDER, request.file.file_name)
        download_file(request.file.file_key, revised_file_path)
        download_time = time.time() - download_start

        # 获取文件大小
        file_size = os.path.getsize(revised_file_path)
        logger.info(f"文件下载完成，耗时: {download_time:.2f}秒，文件大小: {file_size / 1024 / 1024:.2f}MB")

        # 比较文件差异
        diff_start = time.time()
        changes = get_doc_diff_data_v3(revised_file_path)
        diff_time = time.time() - diff_start
        logger.info(f"文档差异分析完成，耗时: {diff_time:.2f}秒，发现 {len(changes)} 个修订")

        # 提取方案号，修订前后版本号
        icf_start = time.time()
        icf_first_table_info = parse_icf_docx(revised_file_path)
        icf_time = time.time() - icf_start
        logger.info(f"ICF信息提取完成，耗时: {icf_time:.2f}秒")

        # 组合返回数据
        response_data = {"changes": changes, "icf_first_table_info": icf_first_table_info}

        # 清理临时文件
        try:
            os.remove(revised_file_path)
            logger.info("临时文件清理完成")
        except Exception as cleanup_error:
            logger.warning(f"临时文件清理失败: {cleanup_error}")

        # 返回解析结果
        end = time.time()
        total_time = end - start
        logger.info(
            f"请求处理完成，总耗时: {total_time:.2f}秒 (下载: {download_time:.2f}s, 差异分析: {diff_time:.2f}s, ICF提取: {icf_time:.2f}s)"
        )
        return make_success(response_data, int(total_time))

    except Exception as e:
        end = time.time()
        total_time = end - start
        logger.error(f"请求处理失败，耗时: {total_time:.2f}秒，错误: {str(e)}")

        # 清理临时文件
        try:
            if "revised_file_path" in locals() and os.path.exists(revised_file_path):
                os.remove(revised_file_path)
        except:
            pass

        return make_fail(500, str(e))


@router.post("/doc/single_diff/v2")
async def compair_doc_diff2(request: SingleFileDiffRequest):
    start = time.time()

    try:
        # 参数检查
        if not request.file:
            return make_fail(400, "file are required.")

        # OSS 文件下载
        revised_file_path = os.path.join(UPLOAD_FOLDER, request.file.file_name)
        download_file(request.file.file_key, revised_file_path)

        # 比较文件差异
        changes = get_doc_diff_data_v2(revised_file_path)

        # Step 4: 返回解析结果
        end = time.time()
        return make_success(changes, int(end - start))
    except Exception as e:
        return make_fail(500, str(e))


@router.post("/doc/diff/report")
async def doc_report(request: DocDiffConvertRequest):
    start = time.time()
    app_logger.info("开始处理文档差异报告生成请求")

    try:
        # 参数检查
        param_check_start = time.time()
        if not request.revised_json or not request.summary_result or not request.icf_first_table_info:
            app_logger.error("请求参数不完整")
            return make_fail(400, "revised_json, summary_result, and icf_first_table_info are required.")

        app_logger.info(
            f"参数检查完成，revised_json条目数: {len(request.revised_json)}, "
            f"summary_result条目数: {len(request.summary_result)}, "
            f"耗时: {time.time() - param_check_start:.3f}秒"
        )

        # 根据组织名选择模板路径
        template_start = time.time()
        current_file_path = os.path.dirname(__file__)
        base_template_dir = os.path.join(current_file_path, "..", "template", "diff")
        template_filename = "revised_diff_template.docx"

        if request.org_name:
            org_template = f"revised_diff_template_{request.org_name.lower()}.docx"
            org_template_path = os.path.join(base_template_dir, org_template)
            if os.path.exists(org_template_path):
                template_filename = org_template
                app_logger.info(f"使用组织专用模板: {template_filename}")

        template_path = os.path.join(base_template_dir, template_filename)
        app_logger.info(f"模板路径确定完成，使用模板: {template_filename}, 耗时: {time.time() - template_start:.3f}秒")

        # 生成报告 - 统一使用优化版本
        report_start = time.time()
        data_size = len(request.revised_json)

        app_logger.info(f"开始生成变更报告，数据量: {data_size}条...")
        file = generate_change_report_optimized(
            request.revised_json, request.summary_result, request.icf_first_table_info, template_path
        )

        report_time = time.time() - report_start
        app_logger.info(f"变更报告生成完成，耗时: {report_time:.3f}秒")

        end = time.time()
        total_time = end - start
        app_logger.info(f"文档差异报告生成请求处理完成，总耗时: {total_time:.3f}秒")
        return make_success(file, int(total_time))
    except Exception as e:
        end = time.time()
        total_time = end - start
        app_logger.error(f"文档差异报告生成失败，耗时: {total_time:.3f}秒，错误: {str(e)}")
        return make_fail(500, str(e))
