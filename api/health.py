import os
import time

from fastapi import APIRouter, HTTPException

from logger.logger import app_logger

router = APIRouter(include_in_schema=False)


@router.get("/health_check")
def health_check():
    """基础健康检查"""
    return {"status": "success", "timestamp": time.time()}


@router.get("/health/detailed")
def detailed_health_check():
    """详细健康检查，包含各组件状态"""
    health_status = {"status": "healthy", "timestamp": time.time(), "components": {}}

    try:
        # 检查 Nacos 配置状态
        try:
            from configurer.yy_nacos import config

            nacos_status = "healthy" if config else "degraded"
            health_status["components"]["nacos"] = {
                "status": nacos_status,
                "configs_loaded": len(config) if config else 0,
            }
        except Exception as e:
            health_status["components"]["nacos"] = {"status": "unhealthy", "error": str(e)}

        # 检查 Redis 连接状态（用于 Celery）
        try:
            from celery_task.celery import celery_app

            broker = celery_app.connection()
            broker.connect()
            broker.close()
            health_status["components"]["redis"] = {"status": "healthy"}
        except Exception as e:
            health_status["components"]["redis"] = {"status": "unhealthy", "error": str(e)}

        # 检查文件系统
        try:
            upload_folder = "/root/yiya-ai-bot/uploads"
            if os.path.exists(upload_folder) and os.access(upload_folder, os.W_OK):
                health_status["components"]["filesystem"] = {"status": "healthy"}
            else:
                health_status["components"]["filesystem"] = {
                    "status": "degraded",
                    "message": "Upload folder not accessible",
                }
        except Exception as e:
            health_status["components"]["filesystem"] = {"status": "unhealthy", "error": str(e)}

        # 根据组件状态确定整体状态
        component_statuses = [comp["status"] for comp in health_status["components"].values()]
        if "unhealthy" in component_statuses:
            health_status["status"] = "unhealthy"
        elif "degraded" in component_statuses:
            health_status["status"] = "degraded"

    except Exception as e:
        app_logger.error(f"健康检查失败: {e}")
        health_status = {"status": "unhealthy", "timestamp": time.time(), "error": str(e)}

    return health_status


@router.get("/readiness")
def readiness_check():
    """就绪检查，用于 Kubernetes 等容器编排"""
    try:
        # 检查关键组件是否就绪
        from configurer.yy_nacos import config

        # 如果 Nacos 配置为空，认为还未就绪
        if not config:
            raise HTTPException(status_code=503, detail="Nacos configuration not loaded")

        return {"status": "ready", "timestamp": time.time()}

    except Exception as e:
        app_logger.warning(f"就绪检查失败: {e}")
        raise HTTPException(status_code=503, detail=f"Service not ready: {str(e)}")


@router.get("/liveness")
def liveness_check():
    """存活检查，用于 Kubernetes 等容器编排"""
    return {"status": "alive", "timestamp": time.time()}
