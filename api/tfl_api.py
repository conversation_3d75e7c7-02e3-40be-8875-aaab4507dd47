#!/usr/bin/env python
import os
import time
from fastapi import APIRouter
from models.result import make_fail, make_success
from models.tfl_vo import RtfFileRequest,TFLMatchRequest
from utils.oss_utils import download_file
from utils.tfl.tfl_utils import extract_tfl_from_rtf
from utils.tfl.tfl_matcher_utils import match_tfls

from logger.logger import app_logger

router = APIRouter(prefix="/tfls", tags=["tfls"],include_in_schema=False)

## rtf 文件存储路径
RTF_FILES_FOLDER = 'rtf'
if not os.path.exists(RTF_FILES_FOLDER):
    os.makedirs(RTF_FILES_FOLDER)

@router.post('/extract-by-rtf')
async def extract_tfls_from_rtf(request: RtfFileRequest):
    """
    解析RTF文件中的tfl数据，并将解析出的文件上传到oss
    """
    start = time.time()

    file_name = request.rtf_file.file_name
    file_key = request.rtf_file.file_key
    rtf_file_path = os.path.join(RTF_FILES_FOLDER, file_name)
    try:
        # 1. 下载rtf 文件
        download_file(file_key, rtf_file_path)

        # 2. 解析rtf文件
        tfl_item = extract_tfl_from_rtf(rtf_file_path, file_key)
        app_logger.info(f"tfl (key={file_key}) is extracted successfully")

        # 3. 返回 tfl_item
        return make_success([tfl_item], int(time.time() - start))
    except Exception as e:
        app_logger.error(f"Error processing file: fkey={file_key}, fname={file_name}, exception={e} ", exc_info=True)
        return make_fail(500, "Internal Server Error")


@router.post(
    '/match',
    summary="匹配In-Text TFLs和TFLs列表"  # OpenAPI中的接口名称
)
async def handle_tfl_matching(request: TFLMatchRequest):
    """
    接收 'in-text TFLs' 和 'TFLs' 列表，使用TF-IDF和余弦相似度进行匹配，
    并将每个TFL分配给最相似的 'in-text TFL'。
    """
    start_time = time.time()
    try:
        # Pydantic模型会自动验证输入。
        # 将Pydantic对象转换为字典列表，以传递给工具函数。
        in_text_tfls_dict = [item.dict() for item in request.inTextTFLs]
        tfls_dict = [item.dict() for item in request.TFLs]

        # 调用核心匹配逻辑
        matched_result = match_tfls(in_text_tfls_dict, tfls_dict)
        app_logger.info(f"TFLs matching completed successfully.")
        duration = time.time() - start_time
        time_cost_ms = int(duration * 1000)

        app_logger.info(f"协议解析成功，耗时: {duration:.2f}s ({time_cost_ms}ms)")
        # 返回成功响应
        return make_success(matched_result, time_cost_ms)

    except ValueError as e:
        # 捕获业务逻辑中定义的错误，例如输入列表为空
        app_logger.warning(f"Validation error in TFL matching: {e}")
        return make_fail(400, str(e))  # 400 Bad Request
    except Exception as e:
        # 捕获其他意外错误
        app_logger.error(f"An unexpected error occurred during TFL matching: {e}", exc_info=True)
        return make_fail(500, "Internal Server Error")