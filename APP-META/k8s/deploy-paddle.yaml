---
apiVersion: apps/v1
kind: Deployment
metadata:
  labels:
    app: yiya-ai-ocr
  name: yiya-ai-ocr
  namespace: yiya-app
spec:
  replicas: 2
  selector:
    matchLabels:
      app: yiya-ai-ocr
  template:
    metadata:
      labels:
        app: yiya-ai-ocr
    spec:
      tolerations:
      - key: "gpushare"
        operator: "Equal"
        value: "true"
        effect: "NoSchedule"
      containers:
        - env:
            - name: CUDA_VISIBLE_DEVICES
              value: '0'
          image: yiya-acr-registry.cn-hangzhou.cr.aliyuncs.com/open/yiya-ocr-base:202412231725
          imagePullPolicy: IfNotPresent
          name: yiya-ai-ocr
          ports:
            - containerPort: 8866
              protocol: TCP
          resources:
            limits:
              cpu: '8'
              memory: 16000Mi
              aliyun.com/gpu-mem: "8"
            requests:
              cpu: '4'
              memory: 8000Mi
          volumeMounts:
            - mountPath: /root/.paddlehub
              name: volume-1734938225090
      dnsPolicy: ClusterFirst
      imagePullSecrets:
        - name: yiya-acr-pub-key
      restartPolicy: Always
      volumes:
        - name: volume-1734938225090
          persistentVolumeClaim:
            #claimName: yiya-ai-bot-paddlehub-pvc
            claimName: llm-models-l40
---
apiVersion: v1
kind: Service
metadata:
  name: yiya-ai-ocr
  namespace: yiya-app
spec:
  ports:
  - port: 8000
    protocol: TCP
    targetPort: 8000
  selector:
    app: yiya-ai-ocr