FROM yiya-acr-registry.cn-hangzhou.cr.aliyuncs.com/yiya/yiya-ai-bot-base:latest

ENV APP_NAME yiya-ai-bot
ENV APP_HOME /root

WORKDIR $APP_HOME/$APP_NAME

# 复制应用程序包（由阿里云构建环境预先生成）
COPY environment/common/app/ $APP_HOME/$APP_NAME/
COPY $APP_NAME.tgz $APP_HOME/$APP_NAME/target/

# 将应用启动脚本、健康检查脚本、nginx配置文件复制到镜像中
COPY environment/common/app/conf/requirements.txt /home/<USER>/$APP_NAME/requirements.txt
COPY environment/common/app/conf/circus.ini $APP_HOME/$APP_NAME/conf/circus.ini
COPY environment/common/app/conf/nacos-defaults.env $APP_HOME/$APP_NAME/conf/nacos-defaults.env

# 安装Python依赖 - 合并所有pip install命令以减少镜像层
RUN pip config set global.index-url https://mirrors.aliyun.com/pypi/simple/ && \
    pip install --upgrade pip && \
    pip install -r /home/<USER>/$APP_NAME/requirements.txt

# 启动应用
CMD ["/bin/bash", "-c", "/root/start.sh"]