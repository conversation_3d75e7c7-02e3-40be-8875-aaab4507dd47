[circus]
check_delay=5
endpoint=tcp://127.0.0.1:5555
pubsub_endpoint=tcp://127.0.0.1:5556
statsd=true
pidfile=/root/logs/circus.pid


[watcher:{{app_name}}]
working_dir=/root/{{app_name}}/target/{{app_name}}
cmd=bash -c "opentelemetry-instrument gunicorn app:app --config gunicorn_conf.py 2>&1 | tee /root/{{app_name}}/logs/application.log"
stop_signal=QUIT
# 启动配置
autostart=true
autorestart=true
restart_delay=10
# 增加启动超时时间，给 Nacos 初始化更多时间
warmup_delay=30
# 防止频繁重启
check_flapping=true
flapping_attempts=3
flapping_window=180
# 日志配置：同时输出到控制台和文件
stdout_stream.class=StdoutStream
stderr_stream.class=StdoutStream


[watcher:{{app_name}}-celery]
working_dir=/root/{{app_name}}/target/{{app_name}}
numprocesses=1
stop_signal=QUIT

# Worker 进程管理配置
autostart=true
autorestart=true
restart_delay=10
# 移除 max_age 配置，避免强制一小时重启
# max_age=3600
# max_age_variance=300

# 简化的健康检查配置
respawn_limit=3
priority=15

# hooks 移除 - Circus 把 hooks 值当作 Python 模块路径解析，导致 watcher 加载失败
# 所有初始化工作通过独立的 watcher 实现
# hooks.before_start=bash /root/{{app_name}}/bin/preload.sh
# hooks.after_start=bash -c "sleep 30 && cd /root/{{app_name}}/target/{{app_name}} && python3.11 -c 'from utils.celery_health_monitor import start_health_monitoring; start_health_monitoring()' &"

# 进程监控配置
warmup_delay=30
check_flapping=true
flapping_attempts=3
flapping_window=180

# 日志配置：同时输出到文件和控制台，便于 kubectl logs 查看
stdout_stream.class=StdoutStream
stderr_stream.class=StdoutStream

# 启动命令：同时保存到 celery.log，错误和正常输出都到控制台
# 修复：使用配置文件中的超时设置（5分钟），而不是命令行硬编码的10分钟
cmd=bash -c "POD_NAME=${HOSTNAME:-$(hostname)} && UNIQUE_NODE_NAME=\"celery@${POD_NAME}-$(date +%s)-$(shuf -i 1000-9999 -n 1)\" && export CELERY_NODE_NAME=\"$UNIQUE_NODE_NAME\" && celery -A celery_task worker --loglevel=info --pool=prefork --concurrency=4 --hostname=\"$UNIQUE_NODE_NAME\" --time-limit=300 --soft-time-limit=240 --max-tasks-per-child=20 --prefetch-multiplier=1 --max-memory-per-child=209715200 --without-gossip --without-mingle --without-heartbeat 2>&1 | tee /root/{{app_name}}/logs/celery.log"


# 移除多余的监控进程，避免多进程冲突
# 只保留一个统一的健康监控，集成所有监控功能

# 精简健康监控 - 保留Redis连接和卡住任务检测，移除冗余的Worker检查
[watcher:{{app_name}}-health-monitor]
working_dir=/root/{{app_name}}/target/{{app_name}}
cmd=bash -c "sleep 120 && python3.11 -c 'from utils.celery_health_monitor import start_health_monitoring; start_health_monitoring()' 2>&1 | tee /root/{{app_name}}/logs/health_monitor.log"
numprocesses=1
autostart=true
autorestart=true
stop_signal=TERM
priority=20
stdout_stream.class=StdoutStream
stderr_stream.class=StdoutStream

[env]
PATH=$PATH
APP_LOG_DIR=$APP_LOG_DIR
APP_STAGE=$APP_STAGE
# K8s 环境变量
KUBERNETES_SERVICE_HOST=$KUBERNETES_SERVICE_HOST
KUBERNETES_PORT=$KUBERNETES_PORT
NACOS_SERVER=$NACOS_SERVER
NACOS_NAMESPACE=$NACOS_NAMESPACE
NACOS_TIMEOUT=$NACOS_TIMEOUT
NACOS_MAX_RETRIES=$NACOS_MAX_RETRIES
NACOS_RETRY_DELAY=$NACOS_RETRY_DELAY
NACOS_INITIAL_DELAY=$NACOS_INITIAL_DELAY
NACOS_ENABLE_IN_CELERY=$NACOS_ENABLE_IN_CELERY
DISABLE_NACOS=$DISABLE_NACOS
# Redis 环境变量
REDIS_HOST=$REDIS_HOST
REDIS_PORT=$REDIS_PORT
REDIS_DB=$REDIS_DB
REDIS_PASSWORD=$REDIS_PASSWORD
REDIS_TASK_DB=$REDIS_TASK_DB
# 任务配置环境变量
TASK_RESULT_EXPIRE_TIME=$TASK_RESULT_EXPIRE_TIME
MAX_CONCURRENT_TASKS=$MAX_CONCURRENT_TASKS
TASK_TIMEOUT=$TASK_TIMEOUT
MAX_RETRY_TIMES=$MAX_RETRY_TIMES

# open-telementry env
OTEL_SERVICE_NAME=yiya-ai-bot
OTEL_EXPORTER_OTLP_PROTOCOL=http/protobuf
OTEL_EXPORTER_OTLP_TRACES_ENDPOINT=http://tracing-analysis-dc-hz-internal.aliyuncs.com/adapt_ftj4u4761v@61a3e3243c0afbf_ftj4u4761v@53df7ad2afe8301/api/otlp/traces
OTEL_EXPORTER_OTLP_METRICS_ENDPOINT=http://tracing-analysis-dc-hz-internal.aliyuncs.com/adapt_ftj4u4761v@61a3e3243c0afbf_ftj4u4761v@53df7ad2afe8301/api/otlp/metrics
