#!/bin/bash

PROG_NAME=$0
ACTION=$1
ALIYUN_MIRROR=" https://mirrors.aliyun.com/pypi/simple/"
NGINX_SKIP=1

usage() {
    echo "Usage: $PROG_NAME {start|start-no-tail|stop|restart|deploy|status|kill|celery-status|celery-restart|celery-check|monitor-start|monitor-stop|monitor-status|monitor-dashboard|soffice-listener}"
    echo ""
    echo "基本命令:"
    echo "  start           - 启动应用和监控服务"
    echo "  stop            - 停止应用和监控服务"
    echo "  restart         - 重启应用和监控服务"
    echo "  status          - 查看应用状态"
    echo ""
    echo "Celery 命令:"
    echo "  celery-status   - 查看 Celery Worker 状态"
    echo "  celery-restart  - 重启 Celery Worker"
    echo "  celery-check    - 检查 Celery 连接"
    echo ""
    echo "  soffice-listener - 启动 libreoffice 监听器"
    echo ""
    echo "监控命令:"
    echo "  monitor-start   - 启动监控服务"
    echo "  monitor-stop    - 停止监控服务"
    echo "  monitor-status  - 查看监控服务状态"
    echo "  monitor-dashboard - 启动监控仪表板"
    exit 1;
}


APP_HOME=$(cd $(dirname ${BASH_SOURCE[0]})/..; pwd)
source "$APP_HOME/bin/setenv.sh"
source "$APP_HOME/bin/hook.sh"

if [ $# -lt 1 ]; then
    usage
fi

CIRCUS_LOG=$APP_HOME/logs/circus
STATUSROOT_HOME=$APP_HOME
shift
TARGET=$*

die() {
    if [ "$#" -gt 0 ]; then
        echo "ERROR:" "$@"
    fi
    exit 128
}

extract_tgz() {
    local tgz_path="$1"
    local dir_path="$2"

    echo "tar -zxvf ${tgz_path} -C ${dir_path}"
    cd "${APP_HOME}/target" || exit 1
    mkdir -p "${dir_path}" || exit 1
    tar -xzvf ${tgz_path} -C ${dir_path} || exit 1
    test -d "${dir_path}" || die "ERROR: no directory: ${dir_path}"
    touch --reference "${tgz_path}" "${tgz_path}.timestamp" || exit 1
}

update_target() {
    local tgz_name="$1"
    local dir_name="$2"

    local tgz_path="${APP_HOME}/target/${tgz_name}"
    local dir_path="${APP_HOME}/target/${dir_name}"

    local error=0
    # dir exists
    echo "dir_path ${dir_path}"
    if [ -d "${dir_path}" ]; then
        # tgz exists
        if [ -f "${tgz_path}" ]; then
            local need_tar=0
            if [ ! -e "${tgz_path}.timestamp" ]; then
                need_tar=1
            else
                local tgz_time=$(stat -L -c "%Y" "${tgz_path}")
                local last_time=$(stat -L -c "%Y" "${tgz_path}.timestamp")
                if [ $tgz_time -gt $last_time ]; then
                    need_tar=1
                fi
            fi
            # tgz is new - extract_tgz
            if [ "${need_tar}" -eq 1 ]; then
                extract_tgz "${tgz_path}" "${dir_path}"
            fi
            # tgz is not new - return SUCCESS
        fi
        # tgz not exists - return SUCCESS
    # dir not exists
    else
        # tgz exists - extract_tgz
        if [ -f "${tgz_path}" ]; then
            extract_tgz "${tgz_path}" "${dir_path}"
        # tgz not exists - return FAIL
        else
            echo "ERROR: ${tgz_path} NOT EXISTS"
            error=1
        fi
    fi

    return $error
}

check_requirements() {
    # touch_virtualenv
    echo "INFO: begin install requirements..."
    if [ -f ${APP_HOME}/target/${APP_NAME}/requirements.txt ]; then
        cp -f ${APP_HOME}/target/${APP_NAME}/requirements.txt ${APP_HOME}/conf/requirements.txt || exit 1
    fi
    # check python requirements
    if ! [ -f "${APP_HOME}/conf/requirements.txt" ]; then
        echo "ERROR: app requirements not found, it's rarely that an app doesn't have any dependencies"
        echo "if you confirm this is right, touch an empty requirements.txt in ${APP_HOME}/conf to avoid this error"
        exit
    fi
    if ! [ -d ${APP_HOME}/logs/ ]; then
        mkdir -p ${APP_HOME}/logs/ || exit 1
    fi
    local requirements_log="${APP_HOME}/logs/${APP_NAME}_requirements.log"
    touch "$requirements_log" || exit
    if [ `cat ${APP_HOME}/conf/requirements.txt | wc -l` -ne 0 ]; then
        pip install --user -r "${APP_HOME}/conf/requirements.txt" -i "${ALIYUN_MIRROR}" |tee -a "${requirements_log}"
        # wait
        local pip_res=$PIPESTATUS
        if [ $pip_res -ne 0 ]; then
            echo "ERROR: requirements not satisfied and auto install failed, please check ${requirements_log}"
            exit 1
        fi
    fi
    # 增加一个安装本地源的 技术保障安全有需要
    if [ -f ${APP_HOME}/target/${APP_NAME}/pypi/requirements.txt ]; then
        pip install --no-index --find-link=file://${APP_HOME}/target/${APP_NAME}/pypi/ -r ${APP_HOME}/target/${APP_NAME}/pypi/requirements.txt |tee -a "${requirements_log}"

        local pip_lres=$PIPESTATUS
        if [ $pip_lres -ne 0 ]; then
            echo "ERROR: requirements not satisfied and auto install failed, please check ${requirements_log}"
            exit 1
        fi
    fi
}
replace_command() {
    local start_linum=$(grep -n -m 1 "\[watcher:" ${APP_HOME}/conf/circus.ini | awk -F: '{print $1}')
    local command=$*
    sed -i "${start_linum}, +5s|cmd=.*|cmd=${command}|g" ${APP_HOME}/conf/circus.ini
}
determine_app_type() {

   if [ -r "${APP_HOME}/target/${APP_NAME}/gunicorn_conf.py" ]; then
        echo "gunicorn web.app:app detected"
        if [ "$ENV" = "production" ]; then
          echo "生产环境：启用 OpenTelemetry"
          #CMD='bash -c "opentelemetry-instrument gunicorn app:app --config gunicorn_conf.py 2>&1 | tee /root/'$APP_NAME'/logs/application.log"'
        else
          echo "非生产环境：禁用 OpenTelemetry"
          CMD='bash -c "gunicorn app:app --config gunicorn_conf.py 2>&1 | tee /root/'$APP_NAME'/logs/application.log"'
          sed -i "/\[watcher:$APP_NAME\]/,/^\[.*\]/ s|^cmd=.*|cmd=$CMD|" ${APP_HOME}/conf/circus.ini
        fi
        return 0
    fi

    if [ -r "${APP_HOME}/target/${APP_NAME}/start_cmd" ]; then
        echo "custom app detected"
        local command=$(cat ${APP_HOME}/target/${APP_NAME}/start_cmd)
        replace_command ${command}
        return 0
    fi

    if [ -r "${APP_HOME}/target/${APP_NAME}/uwsgi.ini" ]; then
        echo "uswgi app detected"
        local command="uwsgi --ini ${APP_HOME}/target/${APP_NAME}/uwsgi.ini --catch-exceptions --protocol=http"
        replace_command ${command}
        return 0
    fi

    echo "ERROR: cannot determine app type"
    echo "you must put one of [uwsgi.ini, gunicorn.ini, start_cmd or bin/appctl.sh|bin/preload.sh] file in ${APP_HOME}/target/${APP_NAME}"
    exit 1
}

set_uwsgi_processes(){
    #获取逻辑cpu数
    if [ $SIGMA_MAX_PROCESSORS_LIMIT ];then
        local cpu_num=${SIGMA_MAX_PROCESSORS_LIMIT}
    else
        local cpu_num=$(cat /proc/cpuinfo| grep "processor"| wc -l)
    fi
    if [ $ENV_TYPE ];then
        #processes数量为cpu*8
        local processes=$((cpu_num*8))
    else
        #processes数量为cpu*6
        local processes=$((cpu_num*6))
    fi
    echo "uwsgi processes is $processes"
    #如果processes为0返回错误
    if [ $processes == 0 ];then
        exit 1
    fi
    #maxRequests数量为cpu*250
    local maxRequests=$((cpu_num*250))
    #如果maxRequests为0返回错误
    if [ $maxRequests == 0 ];then
        exit 1
    fi
    #参数替换
    sed -i "s|processes.*|processes = ${processes}|g" ${APP_HOME}/target/${APP_NAME}/uwsgi.ini
    sed -i "s|max-requests.*|max-requests = ${maxRequests}|g" ${APP_HOME}/target/${APP_NAME}/uwsgi.ini
    return 0
}

check_circu_conf() {
    # check supervisord config files
    if ! [ -f "${APP_HOME}/conf/circus.ini" ]; then
        echo "ERROR: you must create a circus config for your app to make it control with circus"
        exit 1
    fi
    sed -i 's/{{app_name}}/'$(echo $APP_NAME)'/g' ${APP_HOME}/conf/circus.ini
}

check_supervisor_conf() {
    # check supervisord config files
    if ! [ -f "${APP_HOME}/conf/supervisord.conf" ]; then
        echo "ERROR: you must create a supervisord config for your app to make it control with supervisord"
        exit 1
    fi
    sed -i 's/{{app_name}}/'$(echo $APP_NAME)'/g' ${APP_HOME}/conf/circus.conf
}
kill_circus() {
    if { test -r "${CIRCUS_PID}" && kill -0 "$(cat "${CIRCUS_PID}")"; }; then
        kill -9 $(cat "${CIRCUS_PID}")
    else
        echo "circusd not running, do nothing"
    fi
}

start() {
    # delete old $CIRCUS_LOG, keep last 20 logs
    ls "$CIRCUS_LOG".* 2>/dev/null | tail -n +$((20 + 1)) | xargs --no-run-if-empty rm -f
    if [ -e "$CIRCUS_LOG" ]; then
        mv "$CIRCUS_LOG" "$CIRCUS_LOG.$(date '+%Y%m%d%H%M%S')" || exit 1
    fi
    mkdir -p "$(dirname "${CIRCUS_LOG}")" || exit 1
    touch "$CIRCUS_LOG" || exit 1
    # show locale
    locale >> "${CIRCUS_LOG}"

    # print start info to both ${CIRCUS_LOG} and console.
    do_start | tee -a "${CIRCUS_LOG}"
    status
    echo "apps started, use 'appctl.sh status' to check apps status later"
    if [ "${NGINX_SKIP}" -ne "1" ]; then
        echo "start nginx"
        sh "$NGINXCTL" start
    fi

    online

    # 由于日志配置改为输出到 application.log，改为监控该文件
    if [ -f "${APP_HOME}/logs/application.log" ]; then
        tail -f ${APP_HOME}/logs/application.log
    elif [ -f "${APP_HOME}/logs/error.log" ]; then
        tail -f ${APP_HOME}/logs/error.log
    else
        echo "日志文件不存在，跳过日志监控"
        sleep 5
    fi
}

do_start() {
    mkdir -p "${APP_HOME}/target/${APP_NAME}" || exit
    mkdir -p "${APP_HOME}/logs" || exit

    # 创建日志文件，确保启动脚本能找到它们
    touch "${APP_HOME}/logs/application.log"
    touch "${APP_HOME}/logs/error.log"
    mkdir -p "${STATUSROOT_HOME}" || exit

    # update app
    update_target "${APP_NAME}.tgz" "${APP_NAME}" || exit 1

    # check requirements
    # echo "check requirements"
    # check_requirements
    # 如果应用有自定义启动脚本, 那么直接执行应用的脚本
    if [ -r ${APP_HOME}/target/${APP_NAME}/bin/appctl.sh ]; then
        sh ${APP_HOME}/target/${APP_NAME}/bin/appctl.sh start || exit 1
        return $?
    fi

    check_circu_conf

    # 应用启动脚本
    determine_app_type

    echo "start apps"
    beforeStartApp

    # 执行启动前检查
    echo "执行启动前检查..."
    cd "${APP_HOME}/target/${APP_NAME}" || exit 1
    if [ -f "startup_check.py" ]; then
        python3.11 startup_check.py
        if [ $? -ne 0 ]; then
            echo "启动前检查失败，请检查日志"
            exit 1
        fi
        echo "启动前检查通过"
    else
        echo "未找到启动检查脚本，跳过检查"
    fi

    # start circusd
    cd "${APP_HOME}" || exit 1
    pwd
    circusd --daemon ${APP_HOME}/conf/circus.ini

    # start soffice
    soffice_start || exit

}


online() {
    # 如果应用有自定义启动脚本, 那么直接执行应用的脚本
    if [ -r ${APP_HOME}/target/${APP_NAME}/bin/appctl.sh ]; then
        sh ${APP_HOME}/target/${APP_NAME}/bin/appctl.sh online || exit 1
        return $?
    fi
    echo "wait app auto online..."
    for e in $(seq 15); do
        echo -n " $e"
        sleep 1
    done
    echo "app auto online..."
    touch -m $STATUSROOT_HOME/status.yiya || exit 1
}

offline() {
    # 如果应用有自定义启动脚本, 那么直接执行应用的脚本
    if [ -r ${APP_HOME}/target/${APP_NAME}/bin/appctl.sh ]; then
        sh ${APP_HOME}/target/${APP_NAME}/bin/appctl.sh offline || exit 1
        return $?
    fi
    rm -f $STATUSROOT_HOME/status.yiya || exit 1
    echo "wait app offline..."
    for e in $(seq 15); do
        echo -n " $e"
        sleep 1
    done
    echo 'offline success'
}

stop() {
    #先offline
    offline

    if [ "${NGINX_SKIP}" -ne "1" ]; then
        echo "stop nginx"
        sh "$NGINXCTL" stop
    fi
    echo "stop apps"
    beforeStopApp

    # 如果应用有自定义启动脚本, 那么直接执行应用的脚本
    if [ -r ${APP_HOME}/target/${APP_NAME}/bin/appctl.sh ]; then
        sh ${APP_HOME}/target/${APP_NAME}/bin/appctl.sh stop || exit 1
        return $?
    fi

    circusctl stop

    afterStopApp
    echo "stop circusd"
    kill_circus

    echo 'clear reload pid'
    #清理删除所有.fifo文件
    # rm -r ${APP_HOME}/logs/*.fifo
    #清理删除所有uwsgi进程,reload以后supervisort未进行监管的
    pkill -9 uwsgi
}

status() {
    # 如果应用有自定义启动脚本, 那么直接执行应用的脚本
    if [ -r ${APP_HOME}/target/${APP_NAME}/bin/appctl.sh ]; then
        sh ${APP_HOME}/target/${APP_NAME}/bin/appctl.sh status || exit 1
        return $?
    fi
}

# Celery 状态检查
celery_status() {
    echo "=== Celery Worker 状态检查 ==="
    if command -v circusctl >/dev/null 2>&1; then
        echo "Circus 进程状态:"
        circusctl status | grep -E "(${APP_NAME}-celery|${APP_NAME}-celery-monitor)"
        echo ""
    fi

    echo "Celery Worker 进程:"
    ps aux | grep -E "celery.*worker" | grep -v grep || echo "未找到 Celery Worker 进程"
    echo ""

    echo "执行健康检查..."
    if [ -f "${APP_HOME}/bin/celery_realtime_monitor.sh" ]; then
        bash "${APP_HOME}/bin/celery_realtime_monitor.sh" check
    else
        echo "监控脚本不存在"
    fi
}

# 重启 Celery Worker
celery_restart() {
    echo "=== 重启 Celery Worker ==="
    if command -v circusctl >/dev/null 2>&1; then
        echo "使用 circusctl 重启 Celery Worker..."
        circusctl restart ${APP_NAME}-celery
        sleep 3
        echo "重启 Celery 监控..."
        circusctl restart ${APP_NAME}-celery-monitor
        echo "重启完成"
    else
        echo "circusctl 不可用，使用手动重启..."
        pkill -f "celery.*worker"
        sleep 3
        echo "已发送重启信号，等待 Circus 自动拉起"
    fi
}

# Celery 连接检查
celery_check() {
    echo "=== Celery 连接检查 ==="
    cd "${APP_HOME}/target/${APP_NAME}" || exit 1

    echo "检查 Redis 连接..."
    DISABLE_NACOS=true python3.11 -c "
import os
os.environ['DISABLE_NACOS'] = 'true'  # 禁用 Nacos 避免阻塞
from celery_task.celery import celery_app
try:
    broker = celery_app.connection()
    broker.connect()
    print('✅ Redis 连接正常')
except Exception as e:
    print(f'❌ Redis 连接失败: {e}')
"

    echo "检查 Worker 注册状态..."
    DISABLE_NACOS=true python3.11 -c "
import os
os.environ['DISABLE_NACOS'] = 'true'  # 禁用 Nacos 避免阻塞
from celery_task.celery import celery_app
try:
    inspect = celery_app.control.inspect()
    workers = inspect.registered()
    if workers:
        print(f'✅ 发现 {len(workers)} 个注册的 Worker')
        for worker, tasks in workers.items():
            print(f'  Worker: {worker}')
            print(f'  任务数: {len(tasks)}')
    else:
        print('❌ 未发现注册的 Worker')
except Exception as e:
    print(f'❌ Worker 检查失败: {e}')
"

    echo "检查队列状态..."
    python3.11 -c "
from celery_task.celery import celery_app
try:
    inspect = celery_app.control.inspect()
    active = inspect.active()
    reserved = inspect.reserved()

    if active:
        total_active = sum(len(tasks) for tasks in active.values())
        print(f'✅ 活跃任务数: {total_active}')
    else:
        print('ℹ️ 无活跃任务')

    if reserved:
        total_reserved = sum(len(tasks) for tasks in reserved.values())
        print(f'ℹ️ 队列中任务数: {total_reserved}')
    else:
        print('ℹ️ 队列为空')

except Exception as e:
    print(f'❌ 队列检查失败: {e}')
"
}

soffice_start() {
    echo "=== 启动libreoffice监听器 ==="
    nohup soffice --headless \
      --accept="socket,host=127.0.0.1,port=2002;urp;" \
      --nofirststartwizard \
      > /tmp/soffice.log 2>&1 &
    # 等待服务就绪
    echo "Waiting for LibreOffice to start..."
    sleep 3
    # 检查端口
    lsof -i:2002
    echo "Finish liveness check of soffice listener"
}

# 监控服务管理
monitor_start() {
    echo "=== 启动 Celery 监控服务 ==="
    if [ -f "${APP_HOME}/bin/start_monitoring.sh" ]; then
        bash "${APP_HOME}/bin/start_monitoring.sh" start
    else
        echo "❌ 监控启动脚本不存在: ${APP_HOME}/bin/start_monitoring.sh"
        exit 1
    fi
}

monitor_stop() {
    echo "=== 停止 Celery 监控服务 ==="
    if [ -f "${APP_HOME}/bin/start_monitoring.sh" ]; then
        bash "${APP_HOME}/bin/start_monitoring.sh" stop
    else
        echo "❌ 监控启动脚本不存在: ${APP_HOME}/bin/start_monitoring.sh"
        exit 1
    fi
}

monitor_status() {
    echo "=== Celery 监控服务状态 ==="
    if [ -f "${APP_HOME}/bin/start_monitoring.sh" ]; then
        bash "${APP_HOME}/bin/start_monitoring.sh" status
    else
        echo "❌ 监控启动脚本不存在: ${APP_HOME}/bin/start_monitoring.sh"
        exit 1
    fi
}

monitor_dashboard() {
    echo "=== 启动 Celery 监控仪表板 ==="
    if [ -f "${APP_HOME}/bin/monitoring_dashboard.sh" ]; then
        echo "🚀 启动监控仪表板..."
        echo "💡 提示: 按 'q' 退出仪表板"
        echo ""
        bash "${APP_HOME}/bin/monitoring_dashboard.sh"
    else
        echo "❌ 监控仪表板脚本不存在: ${APP_HOME}/bin/monitoring_dashboard.sh"
        exit 1
    fi
}

backup() {
    if [ -f "${APP_HOME}/target/${APP_NAME}.tgz" ]; then
        mkdir -p "${APP_HOME}/target/backup" || exit
        tgz_time=$(date --reference "${APP_HOME}/target/${APP_NAME}.tgz" +"%Y%m%d%H%M%S")
        cp -f "${APP_HOME}/target/${APP_NAME}.tgz" "${APP_HOME}/target/backup/${APP_NAME}.${tgz_time}.tgz"
    fi
}

new_app(){
    if [[ ! -d ${APP_HOME}/target/${APP_NAME} && -f ${APP_HOME}/target/${APP_NAME}.tgz ]]; then
        update_target "${APP_NAME}.tgz" "${APP_NAME}" || exit 1
    fi
}

new_app

case "$ACTION" in
    start)
        start
    ;;
    start-no-tail)
        # delete old $CIRCUS_LOG, keep last 20 logs
        ls "$CIRCUS_LOG".* 2>/dev/null | tail -n +$((20 + 1)) | xargs --no-run-if-empty rm -f
        if [ -e "$CIRCUS_LOG" ]; then
            mv "$CIRCUS_LOG" "$CIRCUS_LOG.$(date '+%Y%m%d%H%M%S')" || exit 1
        fi
        mkdir -p "$(dirname "${CIRCUS_LOG}")" || exit 1
        touch "$CIRCUS_LOG" || exit 1
        locale >> "${CIRCUS_LOG}"
        do_start | tee -a "${CIRCUS_LOG}"
        status
        echo "apps started, use 'appctl.sh status' to check apps status later"
        if [ "${NGINX_SKIP}" -ne "1" ]; then
            echo "start nginx"
            sh "$NGINXCTL" start
        fi
        online
    ;;
    stop)
        stop
    ;;
    restart)
        stop
        start
    ;;
    pubstart)
        stop
        start
    ;;
    deploy)
        stop
        start
        backup
    ;;
    status)
        status
    ;;
    kill)
        kill_circus
    ;;
    online)
        online
    ;;
    offline)
        offline
    ;;
    celery-status)
        celery_status
    ;;
    celery-restart)
        celery_restart
    ;;
    celery-check)
        celery_check
    ;;
    soffice-listener)
        soffice_start
    ;;
    monitor-start)
        monitor_start
    ;;
    monitor-stop)
        monitor_stop
    ;;
    monitor-status)
        monitor_status
    ;;
    monitor-dashboard)
        monitor_dashboard
    ;;
    *)
        usage
    ;;
esac