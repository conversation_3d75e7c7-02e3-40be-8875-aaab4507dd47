# From ubuntu:22.04
FROM yiya-acr-registry.cn-hangzhou.cr.aliyuncs.com/open/python:3.11.0_202501072003

# 设置环境变量
ENV APP_NAME=yiya-ai-bot \
    APP_HOME=/root \
    LANG=zh_CN.UTF-8 \
    LANGUAGE=zh_CN:zh \
    TZ=Asia/Shanghai

COPY deb-sources.list /etc/apt/sources.list

# 创建必要目录并安装系统依赖
RUN mkdir -p $APP_HOME/$APP_NAME/bin \
             $APP_HOME/$APP_NAME/conf \
             $APP_HOME/$APP_NAME/target \
             $APP_HOME/logs/app \
             $APP_HOME/logs/supervisord && \
    apt-get update && \
    DEBIAN_FRONTEND=noninteractive apt-get install -y \
        unar libgl1-mesa-glx libglib2.0-0 tzdata poppler-utils unrtf && \
    ln -sf /usr/share/zoneinfo/Asia/Shanghai /etc/localtime && \
    echo "Asia/Shanghai" > /etc/timezone && \
    rm -rf /var/lib/apt/lists/*

ENV PATH="$PATH:$APP_HOME/.local/bin:/usr/local/python${PYTHON_MAIN_VER}/bin"

# 安装 LibreOffice - 设置环境变量，避免交互式配置
# ENV DEBIAN_FRONTEND=noninteractive

# 安装 LibreOffice - 更新包管理器并安装
RUN apt-get update && \
    apt-get install -y --no-install-recommends \
        # X11 图形依赖（即使 headless 也需要）
        libxinerama1 \
        libxrandr2 \
        libxcursor1 \
        libxext6 \
        libxrender1 \
        libx11-6 \
        libsm6 \
        libice6 \
        libnss3 \
        libdbus-1-3 \
        libfontconfig1 \
        libfreetype6 \
        libglib2.0-0 \
        libpangocairo-1.0-0 \
        libpango-1.0-0 \
        libcairo2 \
        libgdk-pixbuf2.0-0 \
        libcups2 \
        # OpenGL（可选）
        libgl1 \
    && rm -rf /var/lib/apt/lists/*

RUN wget https://yiya-dev.oss-cn-hangzhou.aliyuncs.com/test/libreoffice/LibreOffice_25.2.5_Linux_x86-64_deb.tar.gz
RUN tar -xvf LibreOffice_25.2.5_Linux_x86-64_deb.tar.gz
RUN dpkg -i LibreOffice_25.2.5.2_Linux_x86-64_deb/DEBS/*.deb && apt-get install -f -y
RUN ln -sf /opt/libreoffice25.2/program/soffice /usr/bin/soffice
RUN ls -al /usr/bin/soffice

# 安装 LibreOffice - 验证安装
RUN soffice --version

# 清除安装包
RUN rm -rf LibreOffice_25.2.5.2_Linux_x86-64_deb/
RUN rm -rf LibreOffice_25.2.5_Linux_x86-64_deb.tar.gz

COPY environment/common/app/conf/requirements.txt /home/<USER>/$APP_NAME/requirements.txt

# 安装Python依赖 - 合并所有pip install命令以减少镜像层
RUN pip config set global.index-url https://mirrors.aliyun.com/pypi/simple/ && \
    pip install --upgrade pip && \
    pip install -r /home/<USER>/$APP_NAME/requirements.txt

# 安装监控依赖包
RUN opentelemetry-bootstrap -a install

# 复制最新的启动脚本
COPY environment/common/app/conf/circus.ini $APP_HOME/$APP_NAME/conf/circus.ini
COPY environment/common/app/conf/nacos-defaults.env $APP_HOME/$APP_NAME/conf/nacos-defaults.env
COPY environment/common/app/bin/appctl.sh $APP_HOME/$APP_NAME/bin/appctl.sh
COPY environment/common/app/bin/hook.sh $APP_HOME/$APP_NAME/bin/hook.sh
COPY environment/common/app/bin/setenv.sh $APP_HOME/$APP_NAME/bin/setenv.sh
COPY environment/common/app/bin/preload.sh $APP_HOME/$APP_NAME/bin/preload.sh

# 创建启动脚本并设置权限 - 合并RUN命令
RUN echo "$APP_HOME/$APP_NAME/bin/appctl.sh start" >> $APP_HOME/start.sh && \
    echo "$APP_HOME/$APP_NAME/bin/preload.sh" >> $APP_HOME/health.sh && \
    chmod -R a+x ${APP_HOME}/$APP_NAME/bin/ && \
    chmod +x ${APP_HOME}/*.sh

# 设置执行权限 - 只保留必要的脚本
RUN chmod +x $APP_HOME/$APP_NAME/bin/appctl.sh && \
    chmod +x $APP_HOME/$APP_NAME/bin/hook.sh && \
    chmod +x $APP_HOME/$APP_NAME/bin/setenv.sh && \
    chmod +x $APP_HOME/$APP_NAME/bin/preload.sh

# 启动应用
CMD ["/bin/bash", "-c", "/root/start.sh"]