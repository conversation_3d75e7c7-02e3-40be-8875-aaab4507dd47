<?xml version='1.0' encoding='utf-8'?>
<ct:contentTypeSchema ma:contentTypeName="Document" xmlns:ct="http://schemas.microsoft.com/office/2006/metadata/contentType" ma:contentTypeID="0x010100573256EB82F8D54B81E9E90F38ABA885" xmlns:ma="http://schemas.microsoft.com/office/2006/metadata/properties/metaAttributes" ma:contentTypeDescription="Create a new document." ct:_="" ma:_="" ma:contentTypeScope="" ma:versionID="3b6af627e0ca3391f7c4bef9054a1364" ma:contentTypeVersion="2">
 <xsd:schema ma:fieldsID="2259d189a99997659d579a11e32343d9" targetNamespace="http://schemas.microsoft.com/office/2006/metadata/properties" xmlns:xs="http://www.w3.org/2001/XMLSchema" xmlns:ns2="c4895160-58f7-4877-abb3-ba92b55e5203" xmlns:xsd="http://www.w3.org/2001/XMLSchema" ns2:_="" xmlns:p="http://schemas.microsoft.com/office/2006/metadata/properties" ma:root="true">
  <xsd:import namespace="c4895160-58f7-4877-abb3-ba92b55e5203"/>
  <xsd:element name="properties">
   <xsd:complexType>
    <xsd:sequence>
     <xsd:element name="documentManagement">
      <xsd:complexType>
       <xsd:all>
        <xsd:element ref="ns2:SharedWithUsers" minOccurs="0"/>
        <xsd:element ref="ns2:SharedWithDetails" minOccurs="0"/>
       </xsd:all>
      </xsd:complexType>
     </xsd:element>
    </xsd:sequence>
   </xsd:complexType>
  </xsd:element>
 </xsd:schema>
 <xsd:schema elementFormDefault="qualified" targetNamespace="c4895160-58f7-4877-abb3-ba92b55e5203" xmlns:xs="http://www.w3.org/2001/XMLSchema" xmlns:xsd="http://www.w3.org/2001/XMLSchema" xmlns:pc="http://schemas.microsoft.com/office/infopath/2007/PartnerControls" xmlns:dms="http://schemas.microsoft.com/office/2006/documentManagement/types">
  <xsd:import namespace="http://schemas.microsoft.com/office/2006/documentManagement/types"/>
  <xsd:import namespace="http://schemas.microsoft.com/office/infopath/2007/PartnerControls"/>
  <xsd:element name="SharedWithUsers" nillable="true" ma:index="8" ma:internalName="SharedWithUsers" ma:displayName="Shared With" ma:readOnly="true">
   <xsd:complexType>
    <xsd:complexContent>
     <xsd:extension base="dms:UserMulti">
      <xsd:sequence>
       <xsd:element name="UserInfo" maxOccurs="unbounded" minOccurs="0">
        <xsd:complexType>
         <xsd:sequence>
          <xsd:element name="DisplayName" minOccurs="0" type="xsd:string"/>
          <xsd:element name="AccountId" nillable="true" minOccurs="0" type="dms:UserId"/>
          <xsd:element name="AccountType" minOccurs="0" type="xsd:string"/>
         </xsd:sequence>
        </xsd:complexType>
       </xsd:element>
      </xsd:sequence>
     </xsd:extension>
    </xsd:complexContent>
   </xsd:complexType>
  </xsd:element>
  <xsd:element name="SharedWithDetails" nillable="true" ma:index="9" ma:internalName="SharedWithDetails" ma:displayName="Shared With Details" ma:readOnly="true">
   <xsd:simpleType>
    <xsd:restriction base="dms:Note">
     <xsd:maxLength value="255"/>
    </xsd:restriction>
   </xsd:simpleType>
  </xsd:element>
 </xsd:schema>
 <xsd:schema elementFormDefault="qualified" targetNamespace="http://schemas.openxmlformats.org/package/2006/metadata/core-properties" attributeFormDefault="unqualified" xmlns:xsd="http://www.w3.org/2001/XMLSchema" xmlns:dcterms="http://purl.org/dc/terms/" xmlns="http://schemas.openxmlformats.org/package/2006/metadata/core-properties" xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance" xmlns:dc="http://purl.org/dc/elements/1.1/" blockDefault="#all" xmlns:odoc="http://schemas.microsoft.com/internal/obd">
  <xsd:import namespace="http://purl.org/dc/elements/1.1/" schemaLocation="http://dublincore.org/schemas/xmls/qdc/2003/04/02/dc.xsd"/>
  <xsd:import namespace="http://purl.org/dc/terms/" schemaLocation="http://dublincore.org/schemas/xmls/qdc/2003/04/02/dcterms.xsd"/>
  <xsd:element name="coreProperties" type="CT_coreProperties"/>
  <xsd:complexType name="CT_coreProperties">
   <xsd:all>
    <xsd:element ref="dc:creator" maxOccurs="1" minOccurs="0"/>
    <xsd:element ref="dcterms:created" maxOccurs="1" minOccurs="0"/>
    <xsd:element ref="dc:identifier" maxOccurs="1" minOccurs="0"/>
    <xsd:element name="contentType" maxOccurs="1" minOccurs="0" ma:index="0" type="xsd:string" ma:displayName="Content Type"/>
    <xsd:element ref="dc:title" maxOccurs="1" minOccurs="0" ma:index="4" ma:displayName="Title"/>
    <xsd:element ref="dc:subject" maxOccurs="1" minOccurs="0"/>
    <xsd:element ref="dc:description" maxOccurs="1" minOccurs="0"/>
    <xsd:element name="keywords" maxOccurs="1" minOccurs="0" type="xsd:string"/>
    <xsd:element ref="dc:language" maxOccurs="1" minOccurs="0"/>
    <xsd:element name="category" maxOccurs="1" minOccurs="0" type="xsd:string"/>
    <xsd:element name="version" maxOccurs="1" minOccurs="0" type="xsd:string"/>
    <xsd:element name="revision" maxOccurs="1" minOccurs="0" type="xsd:string">
     <xsd:annotation>
      <xsd:documentation>&#xd;
                        This value indicates the number of saves or revisions. The application is responsible for updating this value after each revision.&#xd;
                    </xsd:documentation>
     </xsd:annotation>
    </xsd:element>
    <xsd:element name="lastModifiedBy" maxOccurs="1" minOccurs="0" type="xsd:string"/>
    <xsd:element ref="dcterms:modified" maxOccurs="1" minOccurs="0"/>
    <xsd:element name="contentStatus" maxOccurs="1" minOccurs="0" type="xsd:string"/>
   </xsd:all>
  </xsd:complexType>
 </xsd:schema>
 <xs:schema elementFormDefault="qualified" targetNamespace="http://schemas.microsoft.com/office/infopath/2007/PartnerControls" xmlns:xs="http://www.w3.org/2001/XMLSchema" attributeFormDefault="unqualified" xmlns:pc="http://schemas.microsoft.com/office/infopath/2007/PartnerControls">
  <xs:element name="Person">
   <xs:complexType>
    <xs:sequence>
     <xs:element ref="pc:DisplayName" minOccurs="0"/>
     <xs:element ref="pc:AccountId" minOccurs="0"/>
     <xs:element ref="pc:AccountType" minOccurs="0"/>
    </xs:sequence>
   </xs:complexType>
  </xs:element>
  <xs:element name="DisplayName" type="xs:string"/>
  <xs:element name="AccountId" type="xs:string"/>
  <xs:element name="AccountType" type="xs:string"/>
  <xs:element name="BDCAssociatedEntity">
   <xs:complexType>
    <xs:sequence>
     <xs:element ref="pc:BDCEntity" maxOccurs="unbounded" minOccurs="0"/>
    </xs:sequence>
    <xs:attribute ref="pc:EntityNamespace"/>
    <xs:attribute ref="pc:EntityName"/>
    <xs:attribute ref="pc:SystemInstanceName"/>
    <xs:attribute ref="pc:AssociationName"/>
   </xs:complexType>
  </xs:element>
  <xs:attribute name="EntityNamespace" type="xs:string"/>
  <xs:attribute name="EntityName" type="xs:string"/>
  <xs:attribute name="SystemInstanceName" type="xs:string"/>
  <xs:attribute name="AssociationName" type="xs:string"/>
  <xs:element name="BDCEntity">
   <xs:complexType>
    <xs:sequence>
     <xs:element ref="pc:EntityDisplayName" minOccurs="0"/>
     <xs:element ref="pc:EntityInstanceReference" minOccurs="0"/>
     <xs:element ref="pc:EntityId1" minOccurs="0"/>
     <xs:element ref="pc:EntityId2" minOccurs="0"/>
     <xs:element ref="pc:EntityId3" minOccurs="0"/>
     <xs:element ref="pc:EntityId4" minOccurs="0"/>
     <xs:element ref="pc:EntityId5" minOccurs="0"/>
    </xs:sequence>
   </xs:complexType>
  </xs:element>
  <xs:element name="EntityDisplayName" type="xs:string"/>
  <xs:element name="EntityInstanceReference" type="xs:string"/>
  <xs:element name="EntityId1" type="xs:string"/>
  <xs:element name="EntityId2" type="xs:string"/>
  <xs:element name="EntityId3" type="xs:string"/>
  <xs:element name="EntityId4" type="xs:string"/>
  <xs:element name="EntityId5" type="xs:string"/>
  <xs:element name="Terms">
   <xs:complexType>
    <xs:sequence>
     <xs:element ref="pc:TermInfo" maxOccurs="unbounded" minOccurs="0"/>
    </xs:sequence>
   </xs:complexType>
  </xs:element>
  <xs:element name="TermInfo">
   <xs:complexType>
    <xs:sequence>
     <xs:element ref="pc:TermName" minOccurs="0"/>
     <xs:element ref="pc:TermId" minOccurs="0"/>
    </xs:sequence>
   </xs:complexType>
  </xs:element>
  <xs:element name="TermName" type="xs:string"/>
  <xs:element name="TermId" type="xs:string"/>
 </xs:schema>
</ct:contentTypeSchema>
