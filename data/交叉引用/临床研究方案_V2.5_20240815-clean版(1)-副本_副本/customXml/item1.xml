<?xml version="1.0"?><ct:contentTypeSchema ct:_="" ma:_="" ma:contentTypeName="文档" ma:contentTypeID="0x010100A02224ECBB30C6409EA437487EF035EB" ma:contentTypeVersion="4" ma:contentTypeDescription="新建文档。" ma:contentTypeScope="" ma:versionID="120e21b2a42ed6b46976ca262dea88de" xmlns:ct="http://schemas.microsoft.com/office/2006/metadata/contentType" xmlns:ma="http://schemas.microsoft.com/office/2006/metadata/properties/metaAttributes">
<xsd:schema targetNamespace="http://schemas.microsoft.com/office/2006/metadata/properties" ma:root="true" ma:fieldsID="8727a64e4f40a0f13a4fd2a6107c18a3" ns3:_="" ns4:_="" xmlns:xsd="http://www.w3.org/2001/XMLSchema" xmlns:xs="http://www.w3.org/2001/XMLSchema" xmlns:p="http://schemas.microsoft.com/office/2006/metadata/properties" xmlns:ns3="902e9be2-a5aa-401d-b3ab-38f3d118d381" xmlns:ns4="152421e9-5e38-47ce-b7f6-4102f3dc0494">
<xsd:import namespace="902e9be2-a5aa-401d-b3ab-38f3d118d381"/>
<xsd:import namespace="152421e9-5e38-47ce-b7f6-4102f3dc0494"/>
<xsd:element name="properties">
<xsd:complexType>
<xsd:sequence>
<xsd:element name="documentManagement">
<xsd:complexType>
<xsd:all>
<xsd:element ref="ns3:_activity" minOccurs="0"/>
<xsd:element ref="ns4:SharedWithUsers" minOccurs="0"/>
<xsd:element ref="ns4:SharedWithDetails" minOccurs="0"/>
<xsd:element ref="ns4:SharingHintHash" minOccurs="0"/>
</xsd:all>
</xsd:complexType>
</xsd:element>
</xsd:sequence>
</xsd:complexType>
</xsd:element>
</xsd:schema>
<xsd:schema targetNamespace="902e9be2-a5aa-401d-b3ab-38f3d118d381" elementFormDefault="qualified" xmlns:xsd="http://www.w3.org/2001/XMLSchema" xmlns:xs="http://www.w3.org/2001/XMLSchema" xmlns:dms="http://schemas.microsoft.com/office/2006/documentManagement/types" xmlns:pc="http://schemas.microsoft.com/office/infopath/2007/PartnerControls">
<xsd:import namespace="http://schemas.microsoft.com/office/2006/documentManagement/types"/>
<xsd:import namespace="http://schemas.microsoft.com/office/infopath/2007/PartnerControls"/>
<xsd:element name="_activity" ma:index="8" nillable="true" ma:displayName="_activity" ma:hidden="true" ma:internalName="_activity">
<xsd:simpleType>
<xsd:restriction base="dms:Note"/>
</xsd:simpleType>
</xsd:element>
</xsd:schema>
<xsd:schema targetNamespace="152421e9-5e38-47ce-b7f6-4102f3dc0494" elementFormDefault="qualified" xmlns:xsd="http://www.w3.org/2001/XMLSchema" xmlns:xs="http://www.w3.org/2001/XMLSchema" xmlns:dms="http://schemas.microsoft.com/office/2006/documentManagement/types" xmlns:pc="http://schemas.microsoft.com/office/infopath/2007/PartnerControls">
<xsd:import namespace="http://schemas.microsoft.com/office/2006/documentManagement/types"/>
<xsd:import namespace="http://schemas.microsoft.com/office/infopath/2007/PartnerControls"/>
<xsd:element name="SharedWithUsers" ma:index="9" nillable="true" ma:displayName="共享对象:" ma:internalName="SharedWithUsers" ma:readOnly="true">
<xsd:complexType>
<xsd:complexContent>
<xsd:extension base="dms:UserMulti">
<xsd:sequence>
<xsd:element name="UserInfo" minOccurs="0" maxOccurs="unbounded">
<xsd:complexType>
<xsd:sequence>
<xsd:element name="DisplayName" type="xsd:string" minOccurs="0"/>
<xsd:element name="AccountId" type="dms:UserId" minOccurs="0" nillable="true"/>
<xsd:element name="AccountType" type="xsd:string" minOccurs="0"/>
</xsd:sequence>
</xsd:complexType>
</xsd:element>
</xsd:sequence>
</xsd:extension>
</xsd:complexContent>
</xsd:complexType>
</xsd:element>
<xsd:element name="SharedWithDetails" ma:index="10" nillable="true" ma:displayName="共享对象详细信息" ma:internalName="SharedWithDetails" ma:readOnly="true">
<xsd:simpleType>
<xsd:restriction base="dms:Note">
<xsd:maxLength value="255"/>
</xsd:restriction>
</xsd:simpleType>
</xsd:element>
<xsd:element name="SharingHintHash" ma:index="11" nillable="true" ma:displayName="共享提示哈希" ma:hidden="true" ma:internalName="SharingHintHash" ma:readOnly="true">
<xsd:simpleType>
<xsd:restriction base="dms:Text"/>
</xsd:simpleType>
</xsd:element>
</xsd:schema>
<xsd:schema targetNamespace="http://schemas.openxmlformats.org/package/2006/metadata/core-properties" elementFormDefault="qualified" attributeFormDefault="unqualified" blockDefault="#all" xmlns="http://schemas.openxmlformats.org/package/2006/metadata/core-properties" xmlns:xsd="http://www.w3.org/2001/XMLSchema" xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance" xmlns:dc="http://purl.org/dc/elements/1.1/" xmlns:dcterms="http://purl.org/dc/terms/" xmlns:odoc="http://schemas.microsoft.com/internal/obd">
<xsd:import namespace="http://purl.org/dc/elements/1.1/" schemaLocation="http://dublincore.org/schemas/xmls/qdc/2003/04/02/dc.xsd"/>
<xsd:import namespace="http://purl.org/dc/terms/" schemaLocation="http://dublincore.org/schemas/xmls/qdc/2003/04/02/dcterms.xsd"/>
<xsd:element name="coreProperties" type="CT_coreProperties"/>
<xsd:complexType name="CT_coreProperties">
<xsd:all>
<xsd:element ref="dc:creator" minOccurs="0" maxOccurs="1"/>
<xsd:element ref="dcterms:created" minOccurs="0" maxOccurs="1"/>
<xsd:element ref="dc:identifier" minOccurs="0" maxOccurs="1"/>
<xsd:element name="contentType" minOccurs="0" maxOccurs="1" type="xsd:string" ma:index="0" ma:displayName="内容类型"/>
<xsd:element ref="dc:title" minOccurs="0" maxOccurs="1" ma:index="4" ma:displayName="标题"/>
<xsd:element ref="dc:subject" minOccurs="0" maxOccurs="1"/>
<xsd:element ref="dc:description" minOccurs="0" maxOccurs="1"/>
<xsd:element name="keywords" minOccurs="0" maxOccurs="1" type="xsd:string"/>
<xsd:element ref="dc:language" minOccurs="0" maxOccurs="1"/>
<xsd:element name="category" minOccurs="0" maxOccurs="1" type="xsd:string"/>
<xsd:element name="version" minOccurs="0" maxOccurs="1" type="xsd:string"/>
<xsd:element name="revision" minOccurs="0" maxOccurs="1" type="xsd:string">
<xsd:annotation>
<xsd:documentation>
                        This value indicates the number of saves or revisions. The application is responsible for updating this value after each revision.
                    </xsd:documentation>
</xsd:annotation>
</xsd:element>
<xsd:element name="lastModifiedBy" minOccurs="0" maxOccurs="1" type="xsd:string"/>
<xsd:element ref="dcterms:modified" minOccurs="0" maxOccurs="1"/>
<xsd:element name="contentStatus" minOccurs="0" maxOccurs="1" type="xsd:string"/>
</xsd:all>
</xsd:complexType>
</xsd:schema>
<xs:schema targetNamespace="http://schemas.microsoft.com/office/infopath/2007/PartnerControls" elementFormDefault="qualified" attributeFormDefault="unqualified" xmlns:pc="http://schemas.microsoft.com/office/infopath/2007/PartnerControls" xmlns:xs="http://www.w3.org/2001/XMLSchema">
<xs:element name="Person">
<xs:complexType>
<xs:sequence>
<xs:element ref="pc:DisplayName" minOccurs="0"></xs:element>
<xs:element ref="pc:AccountId" minOccurs="0"></xs:element>
<xs:element ref="pc:AccountType" minOccurs="0"></xs:element>
</xs:sequence>
</xs:complexType>
</xs:element>
<xs:element name="DisplayName" type="xs:string"></xs:element>
<xs:element name="AccountId" type="xs:string"></xs:element>
<xs:element name="AccountType" type="xs:string"></xs:element>
<xs:element name="BDCAssociatedEntity">
<xs:complexType>
<xs:sequence>
<xs:element ref="pc:BDCEntity" minOccurs="0" maxOccurs="unbounded"></xs:element>
</xs:sequence>
<xs:attribute ref="pc:EntityNamespace"></xs:attribute>
<xs:attribute ref="pc:EntityName"></xs:attribute>
<xs:attribute ref="pc:SystemInstanceName"></xs:attribute>
<xs:attribute ref="pc:AssociationName"></xs:attribute>
</xs:complexType>
</xs:element>
<xs:attribute name="EntityNamespace" type="xs:string"></xs:attribute>
<xs:attribute name="EntityName" type="xs:string"></xs:attribute>
<xs:attribute name="SystemInstanceName" type="xs:string"></xs:attribute>
<xs:attribute name="AssociationName" type="xs:string"></xs:attribute>
<xs:element name="BDCEntity">
<xs:complexType>
<xs:sequence>
<xs:element ref="pc:EntityDisplayName" minOccurs="0"></xs:element>
<xs:element ref="pc:EntityInstanceReference" minOccurs="0"></xs:element>
<xs:element ref="pc:EntityId1" minOccurs="0"></xs:element>
<xs:element ref="pc:EntityId2" minOccurs="0"></xs:element>
<xs:element ref="pc:EntityId3" minOccurs="0"></xs:element>
<xs:element ref="pc:EntityId4" minOccurs="0"></xs:element>
<xs:element ref="pc:EntityId5" minOccurs="0"></xs:element>
</xs:sequence>
</xs:complexType>
</xs:element>
<xs:element name="EntityDisplayName" type="xs:string"></xs:element>
<xs:element name="EntityInstanceReference" type="xs:string"></xs:element>
<xs:element name="EntityId1" type="xs:string"></xs:element>
<xs:element name="EntityId2" type="xs:string"></xs:element>
<xs:element name="EntityId3" type="xs:string"></xs:element>
<xs:element name="EntityId4" type="xs:string"></xs:element>
<xs:element name="EntityId5" type="xs:string"></xs:element>
<xs:element name="Terms">
<xs:complexType>
<xs:sequence>
<xs:element ref="pc:TermInfo" minOccurs="0" maxOccurs="unbounded"></xs:element>
</xs:sequence>
</xs:complexType>
</xs:element>
<xs:element name="TermInfo">
<xs:complexType>
<xs:sequence>
<xs:element ref="pc:TermName" minOccurs="0"></xs:element>
<xs:element ref="pc:TermId" minOccurs="0"></xs:element>
</xs:sequence>
</xs:complexType>
</xs:element>
<xs:element name="TermName" type="xs:string"></xs:element>
<xs:element name="TermId" type="xs:string"></xs:element>
</xs:schema>
</ct:contentTypeSchema>