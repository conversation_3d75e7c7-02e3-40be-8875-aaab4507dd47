<?xml version="1.0" encoding="utf-8"?><ct:contentTypeSchema ct:_="" ma:_="" ma:contentTypeName="Document" ma:contentTypeID="0x010100FF747EB7F2F0744486655762D4C58A73" ma:contentTypeVersion="10" ma:contentTypeDescription="Create a new document." ma:contentTypeScope="" ma:versionID="bcf5b7f85ac7f27e9b9ced86fa434625" xmlns:ct="http://schemas.microsoft.com/office/2006/metadata/contentType" xmlns:ma="http://schemas.microsoft.com/office/2006/metadata/properties/metaAttributes">
<xsd:schema targetNamespace="http://schemas.microsoft.com/office/2006/metadata/properties" ma:root="true" ma:fieldsID="aeeb922183de8c782af04cebfeaab5d3" ns2:_="" ns3:_="" ns4:_="" xmlns:xsd="http://www.w3.org/2001/XMLSchema" xmlns:xs="http://www.w3.org/2001/XMLSchema" xmlns:p="http://schemas.microsoft.com/office/2006/metadata/properties" xmlns:ns2="44a56295-c29e-4898-8136-a54736c65b82" xmlns:ns3="0f8d8d86-0f23-432e-88cc-f48f84a8a9b8" xmlns:ns4="d6b7bcdb-fc55-433d-9f51-115af91e804e">
<xsd:import namespace="44a56295-c29e-4898-8136-a54736c65b82"/>
<xsd:import namespace="0f8d8d86-0f23-432e-88cc-f48f84a8a9b8"/>
<xsd:import namespace="d6b7bcdb-fc55-433d-9f51-115af91e804e"/>
<xsd:element name="properties">
<xsd:complexType>
<xsd:sequence>
<xsd:element name="documentManagement">
<xsd:complexType>
<xsd:all>
<xsd:element ref="ns2:Descriptions" minOccurs="0"/>
<xsd:element ref="ns2:Keyword" minOccurs="0"/>
<xsd:element ref="ns3:Section_x0020_Sort" minOccurs="0"/>
<xsd:element ref="ns3:Library" minOccurs="0"/>
<xsd:element ref="ns3:Project_x0020_Type" minOccurs="0"/>
<xsd:element ref="ns3:MediaServiceMetadata" minOccurs="0"/>
<xsd:element ref="ns3:MediaServiceFastMetadata" minOccurs="0"/>
<xsd:element ref="ns4:SharedWithUsers" minOccurs="0"/>
<xsd:element ref="ns4:SharedWithDetails" minOccurs="0"/>
<xsd:element ref="ns3:Journey" minOccurs="0"/>
<xsd:element ref="ns3:Resource_x0020_Type" minOccurs="0"/>
</xsd:all>
</xsd:complexType>
</xsd:element>
</xsd:sequence>
</xsd:complexType>
</xsd:element>
</xsd:schema>
<xsd:schema targetNamespace="44a56295-c29e-4898-8136-a54736c65b82" elementFormDefault="qualified" xmlns:xsd="http://www.w3.org/2001/XMLSchema" xmlns:xs="http://www.w3.org/2001/XMLSchema" xmlns:dms="http://schemas.microsoft.com/office/2006/documentManagement/types" xmlns:pc="http://schemas.microsoft.com/office/infopath/2007/PartnerControls">
<xsd:import namespace="http://schemas.microsoft.com/office/2006/documentManagement/types"/>
<xsd:import namespace="http://schemas.microsoft.com/office/infopath/2007/PartnerControls"/>
<xsd:element name="Descriptions" ma:index="8" nillable="true" ma:displayName="Descriptions" ma:description="Describe your document to make it appear at the top of search results" ma:internalName="Descriptions">
<xsd:simpleType>
<xsd:restriction base="dms:Note">
<xsd:maxLength value="255"/>
</xsd:restriction>
</xsd:simpleType>
</xsd:element>
<xsd:element name="Keyword" ma:index="9" nillable="true" ma:displayName="Keyword" ma:description="Enter list of terms separated by semi-colon(;)" ma:internalName="Keyword">
<xsd:simpleType>
<xsd:restriction base="dms:Text">
<xsd:maxLength value="255"/>
</xsd:restriction>
</xsd:simpleType>
</xsd:element>
</xsd:schema>
<xsd:schema targetNamespace="0f8d8d86-0f23-432e-88cc-f48f84a8a9b8" elementFormDefault="qualified" xmlns:xsd="http://www.w3.org/2001/XMLSchema" xmlns:xs="http://www.w3.org/2001/XMLSchema" xmlns:dms="http://schemas.microsoft.com/office/2006/documentManagement/types" xmlns:pc="http://schemas.microsoft.com/office/infopath/2007/PartnerControls">
<xsd:import namespace="http://schemas.microsoft.com/office/2006/documentManagement/types"/>
<xsd:import namespace="http://schemas.microsoft.com/office/infopath/2007/PartnerControls"/>
<xsd:element name="Section_x0020_Sort" ma:index="10" nillable="true" ma:displayName="Section Sort" ma:internalName="Section_x0020_Sort">
<xsd:simpleType>
<xsd:restriction base="dms:Number"/>
</xsd:simpleType>
</xsd:element>
<xsd:element name="Library" ma:index="11" nillable="true" ma:displayName="Library" ma:default="Evidence Connect" ma:internalName="Library" ma:requiredMultiChoice="true">
<xsd:complexType>
<xsd:complexContent>
<xsd:extension base="dms:MultiChoice">
<xsd:sequence>
<xsd:element name="Value" maxOccurs="unbounded" minOccurs="0" nillable="true">
<xsd:simpleType>
<xsd:restriction base="dms:Choice">
<xsd:enumeration value="Evidence Connect"/>
<xsd:enumeration value="Internally Sponsored Research"/>
<xsd:enumeration value="Externally Sponsored Research"/>
<xsd:enumeration value="Early Access Programmes"/>
</xsd:restriction>
</xsd:simpleType>
</xsd:element>
</xsd:sequence>
</xsd:extension>
</xsd:complexContent>
</xsd:complexType>
</xsd:element>
<xsd:element name="Project_x0020_Type" ma:index="12" nillable="true" ma:displayName="Project Type" ma:internalName="Project_x0020_Type" ma:requiredMultiChoice="true">
<xsd:complexType>
<xsd:complexContent>
<xsd:extension base="dms:MultiChoice">
<xsd:sequence>
<xsd:element name="Value" maxOccurs="unbounded" minOccurs="0" nillable="true">
<xsd:simpleType>
<xsd:restriction base="dms:Choice">
<xsd:enumeration value="Interventional (experimental)"/>
<xsd:enumeration value="Observational (generally non-interventional)"/>
<xsd:enumeration value="Evidence Generation Support"/>
<xsd:enumeration value="Clinical or Observational ESR"/>
<xsd:enumeration value="Non-Clinical ESR"/>
<xsd:enumeration value="Early Access Programmes"/>
</xsd:restriction>
</xsd:simpleType>
</xsd:element>
</xsd:sequence>
</xsd:extension>
</xsd:complexContent>
</xsd:complexType>
</xsd:element>
<xsd:element name="MediaServiceMetadata" ma:index="13" nillable="true" ma:displayName="MediaServiceMetadata" ma:hidden="true" ma:internalName="MediaServiceMetadata" ma:readOnly="true">
<xsd:simpleType>
<xsd:restriction base="dms:Note"/>
</xsd:simpleType>
</xsd:element>
<xsd:element name="MediaServiceFastMetadata" ma:index="14" nillable="true" ma:displayName="MediaServiceFastMetadata" ma:hidden="true" ma:internalName="MediaServiceFastMetadata" ma:readOnly="true">
<xsd:simpleType>
<xsd:restriction base="dms:Note"/>
</xsd:simpleType>
</xsd:element>
<xsd:element name="Journey" ma:index="17" nillable="true" ma:displayName="Journey" ma:internalName="Journey">
<xsd:complexType>
<xsd:complexContent>
<xsd:extension base="dms:MultiChoice">
<xsd:sequence>
<xsd:element name="Value" maxOccurs="unbounded" minOccurs="0" nillable="true">
<xsd:simpleType>
<xsd:restriction base="dms:Choice">
<xsd:enumeration value="ISR Interventional (Experimental) Study"/>
<xsd:enumeration value="ISR Observational Study"/>
<xsd:enumeration value="ISR Evidence Generation Support"/>
<xsd:enumeration value="ESR Clinical"/>
<xsd:enumeration value="ESR Non-Clinical"/>
<xsd:enumeration value="ESR Observational"/>
<xsd:enumeration value="Early Access Request"/>
</xsd:restriction>
</xsd:simpleType>
</xsd:element>
</xsd:sequence>
</xsd:extension>
</xsd:complexContent>
</xsd:complexType>
</xsd:element>
<xsd:element name="Resource_x0020_Type" ma:index="18" nillable="true" ma:displayName="Resource Type" ma:format="Dropdown" ma:internalName="Resource_x0020_Type">
<xsd:simpleType>
<xsd:restriction base="dms:Choice">
<xsd:enumeration value="Process Job Aids"/>
<xsd:enumeration value="Process Documents &amp; Templates"/>
<xsd:enumeration value="Release Notes"/>
<xsd:enumeration value="Tool Tips"/>
<xsd:enumeration value="Video Guides – ESR System &amp; Process"/>
<xsd:enumeration value="Learning Journeys"/>
<xsd:enumeration value="User Guides &amp; Videos"/>
</xsd:restriction>
</xsd:simpleType>
</xsd:element>
</xsd:schema>
<xsd:schema targetNamespace="d6b7bcdb-fc55-433d-9f51-115af91e804e" elementFormDefault="qualified" xmlns:xsd="http://www.w3.org/2001/XMLSchema" xmlns:xs="http://www.w3.org/2001/XMLSchema" xmlns:dms="http://schemas.microsoft.com/office/2006/documentManagement/types" xmlns:pc="http://schemas.microsoft.com/office/infopath/2007/PartnerControls">
<xsd:import namespace="http://schemas.microsoft.com/office/2006/documentManagement/types"/>
<xsd:import namespace="http://schemas.microsoft.com/office/infopath/2007/PartnerControls"/>
<xsd:element name="SharedWithUsers" ma:index="15" nillable="true" ma:displayName="Shared With" ma:internalName="SharedWithUsers" ma:readOnly="true">
<xsd:complexType>
<xsd:complexContent>
<xsd:extension base="dms:UserMulti">
<xsd:sequence>
<xsd:element name="UserInfo" minOccurs="0" maxOccurs="unbounded">
<xsd:complexType>
<xsd:sequence>
<xsd:element name="DisplayName" type="xsd:string" minOccurs="0"/>
<xsd:element name="AccountId" type="dms:UserId" minOccurs="0" nillable="true"/>
<xsd:element name="AccountType" type="xsd:string" minOccurs="0"/>
</xsd:sequence>
</xsd:complexType>
</xsd:element>
</xsd:sequence>
</xsd:extension>
</xsd:complexContent>
</xsd:complexType>
</xsd:element>
<xsd:element name="SharedWithDetails" ma:index="16" nillable="true" ma:displayName="Shared With Details" ma:internalName="SharedWithDetails" ma:readOnly="true">
<xsd:simpleType>
<xsd:restriction base="dms:Note">
<xsd:maxLength value="255"/>
</xsd:restriction>
</xsd:simpleType>
</xsd:element>
</xsd:schema>
<xsd:schema targetNamespace="http://schemas.openxmlformats.org/package/2006/metadata/core-properties" elementFormDefault="qualified" attributeFormDefault="unqualified" blockDefault="#all" xmlns="http://schemas.openxmlformats.org/package/2006/metadata/core-properties" xmlns:xsd="http://www.w3.org/2001/XMLSchema" xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance" xmlns:dc="http://purl.org/dc/elements/1.1/" xmlns:dcterms="http://purl.org/dc/terms/" xmlns:odoc="http://schemas.microsoft.com/internal/obd">
<xsd:import namespace="http://purl.org/dc/elements/1.1/" schemaLocation="http://dublincore.org/schemas/xmls/qdc/2003/04/02/dc.xsd"/>
<xsd:import namespace="http://purl.org/dc/terms/" schemaLocation="http://dublincore.org/schemas/xmls/qdc/2003/04/02/dcterms.xsd"/>
<xsd:element name="coreProperties" type="CT_coreProperties"/>
<xsd:complexType name="CT_coreProperties">
<xsd:all>
<xsd:element ref="dc:creator" minOccurs="0" maxOccurs="1"/>
<xsd:element ref="dcterms:created" minOccurs="0" maxOccurs="1"/>
<xsd:element ref="dc:identifier" minOccurs="0" maxOccurs="1"/>
<xsd:element name="contentType" minOccurs="0" maxOccurs="1" type="xsd:string" ma:index="0" ma:displayName="Content Type"/>
<xsd:element ref="dc:title" minOccurs="0" maxOccurs="1" ma:index="4" ma:displayName="Title"/>
<xsd:element ref="dc:subject" minOccurs="0" maxOccurs="1"/>
<xsd:element ref="dc:description" minOccurs="0" maxOccurs="1"/>
<xsd:element name="keywords" minOccurs="0" maxOccurs="1" type="xsd:string"/>
<xsd:element ref="dc:language" minOccurs="0" maxOccurs="1"/>
<xsd:element name="category" minOccurs="0" maxOccurs="1" type="xsd:string"/>
<xsd:element name="version" minOccurs="0" maxOccurs="1" type="xsd:string"/>
<xsd:element name="revision" minOccurs="0" maxOccurs="1" type="xsd:string">
<xsd:annotation>
<xsd:documentation>
                        This value indicates the number of saves or revisions. The application is responsible for updating this value after each revision.
                    </xsd:documentation>
</xsd:annotation>
</xsd:element>
<xsd:element name="lastModifiedBy" minOccurs="0" maxOccurs="1" type="xsd:string"/>
<xsd:element ref="dcterms:modified" minOccurs="0" maxOccurs="1"/>
<xsd:element name="contentStatus" minOccurs="0" maxOccurs="1" type="xsd:string"/>
</xsd:all>
</xsd:complexType>
</xsd:schema>
<xs:schema targetNamespace="http://schemas.microsoft.com/office/infopath/2007/PartnerControls" elementFormDefault="qualified" attributeFormDefault="unqualified" xmlns:pc="http://schemas.microsoft.com/office/infopath/2007/PartnerControls" xmlns:xs="http://www.w3.org/2001/XMLSchema">
<xs:element name="Person">
<xs:complexType>
<xs:sequence>
<xs:element ref="pc:DisplayName" minOccurs="0"></xs:element>
<xs:element ref="pc:AccountId" minOccurs="0"></xs:element>
<xs:element ref="pc:AccountType" minOccurs="0"></xs:element>
</xs:sequence>
</xs:complexType>
</xs:element>
<xs:element name="DisplayName" type="xs:string"></xs:element>
<xs:element name="AccountId" type="xs:string"></xs:element>
<xs:element name="AccountType" type="xs:string"></xs:element>
<xs:element name="BDCAssociatedEntity">
<xs:complexType>
<xs:sequence>
<xs:element ref="pc:BDCEntity" minOccurs="0" maxOccurs="unbounded"></xs:element>
</xs:sequence>
<xs:attribute ref="pc:EntityNamespace"></xs:attribute>
<xs:attribute ref="pc:EntityName"></xs:attribute>
<xs:attribute ref="pc:SystemInstanceName"></xs:attribute>
<xs:attribute ref="pc:AssociationName"></xs:attribute>
</xs:complexType>
</xs:element>
<xs:attribute name="EntityNamespace" type="xs:string"></xs:attribute>
<xs:attribute name="EntityName" type="xs:string"></xs:attribute>
<xs:attribute name="SystemInstanceName" type="xs:string"></xs:attribute>
<xs:attribute name="AssociationName" type="xs:string"></xs:attribute>
<xs:element name="BDCEntity">
<xs:complexType>
<xs:sequence>
<xs:element ref="pc:EntityDisplayName" minOccurs="0"></xs:element>
<xs:element ref="pc:EntityInstanceReference" minOccurs="0"></xs:element>
<xs:element ref="pc:EntityId1" minOccurs="0"></xs:element>
<xs:element ref="pc:EntityId2" minOccurs="0"></xs:element>
<xs:element ref="pc:EntityId3" minOccurs="0"></xs:element>
<xs:element ref="pc:EntityId4" minOccurs="0"></xs:element>
<xs:element ref="pc:EntityId5" minOccurs="0"></xs:element>
</xs:sequence>
</xs:complexType>
</xs:element>
<xs:element name="EntityDisplayName" type="xs:string"></xs:element>
<xs:element name="EntityInstanceReference" type="xs:string"></xs:element>
<xs:element name="EntityId1" type="xs:string"></xs:element>
<xs:element name="EntityId2" type="xs:string"></xs:element>
<xs:element name="EntityId3" type="xs:string"></xs:element>
<xs:element name="EntityId4" type="xs:string"></xs:element>
<xs:element name="EntityId5" type="xs:string"></xs:element>
<xs:element name="Terms">
<xs:complexType>
<xs:sequence>
<xs:element ref="pc:TermInfo" minOccurs="0" maxOccurs="unbounded"></xs:element>
</xs:sequence>
</xs:complexType>
</xs:element>
<xs:element name="TermInfo">
<xs:complexType>
<xs:sequence>
<xs:element ref="pc:TermName" minOccurs="0"></xs:element>
<xs:element ref="pc:TermId" minOccurs="0"></xs:element>
</xs:sequence>
</xs:complexType>
</xs:element>
<xs:element name="TermName" type="xs:string"></xs:element>
<xs:element name="TermId" type="xs:string"></xs:element>
</xs:schema>
</ct:contentTypeSchema>