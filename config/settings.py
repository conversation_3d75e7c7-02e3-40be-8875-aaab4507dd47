import os

from pydantic import field_validator
from pydantic_settings import BaseSettings


class Settings(BaseSettings):
    REDIS_HOST: str = "***********"
    REDIS_PORT: int = 6379
    REDIS_DB: int = 4  # 使用 db4 作为协议解析专用 broker
    REDIS_TASK_DB: int = 4  # 使用 db4 作为结果存储
    REDIS_PASSWORD: str = "wtg2024@"  # TODO: 移除硬编码，使用环境变量

    REDIS_DB_LOCAL: int = 4  # 本地环境也使用 db4
    REDIS_TASK_DB_LOCAL: int = 4

    # 任务配置
    TASK_RESULT_EXPIRE_TIME: int = 60 * 60 * 24  # 24小时
    MAX_CONCURRENT_TASKS: int = 10
    TASK_TIMEOUT: int = 3600
    MAX_RETRY_TIMES: int = 3

    @field_validator("REDIS_PORT", mode="before")
    @classmethod
    def parse_redis_port(cls, v):
        """处理 REDIS_PORT 空值"""
        if v == "" or v is None:
            return 6379  # 默认值
        if isinstance(v, str):
            try:
                return int(v)
            except ValueError:
                return 6379
        return v

    @field_validator("REDIS_DB", mode="before")
    @classmethod
    def parse_redis_db(cls, v):
        """处理 REDIS_DB 空值"""
        if v == "" or v is None:
            return 4  # 默认值
        if isinstance(v, str):
            try:
                return int(v)
            except ValueError:
                return 4
        return v

    @field_validator("REDIS_TASK_DB", mode="before")
    @classmethod
    def parse_redis_task_db(cls, v):
        """处理 REDIS_TASK_DB 空值"""
        if v == "" or v is None:
            return 4  # 默认值
        if isinstance(v, str):
            try:
                return int(v)
            except ValueError:
                return 4
        return v

    @field_validator("TASK_RESULT_EXPIRE_TIME", mode="before")
    @classmethod
    def parse_task_result_expire_time(cls, v):
        """处理 TASK_RESULT_EXPIRE_TIME 空值"""
        if v == "" or v is None:
            return 60 * 60 * 24  # 默认值：24小时
        if isinstance(v, str):
            try:
                return int(v)
            except ValueError:
                return 60 * 60 * 24
        return v

    @field_validator("MAX_CONCURRENT_TASKS", mode="before")
    @classmethod
    def parse_max_concurrent_tasks(cls, v):
        """处理 MAX_CONCURRENT_TASKS 空值"""
        if v == "" or v is None:
            return 10  # 默认值
        if isinstance(v, str):
            try:
                return int(v)
            except ValueError:
                return 10
        return v

    @field_validator("TASK_TIMEOUT", mode="before")
    @classmethod
    def parse_task_timeout(cls, v):
        """处理 TASK_TIMEOUT 空值"""
        if v == "" or v is None:
            return 3600  # 默认值
        if isinstance(v, str):
            try:
                return int(v)
            except ValueError:
                return 3600
        return v

    @field_validator("MAX_RETRY_TIMES", mode="before")
    @classmethod
    def parse_max_retry_times(cls, v):
        """处理 MAX_RETRY_TIMES 空值"""
        if v == "" or v is None:
            return 3  # 默认值
        if isinstance(v, str):
            try:
                return int(v)
            except ValueError:
                return 3
        return v

    @field_validator("REDIS_HOST", mode="before")
    @classmethod
    def parse_redis_host(cls, v):
        """处理 REDIS_HOST 空值"""
        if v == "" or v is None:
            return "***********"  # 默认值
        return v

    @field_validator("REDIS_PASSWORD", mode="before")
    @classmethod
    def parse_redis_password(cls, v):
        """处理 REDIS_PASSWORD 空值"""
        if v == "" or v is None:
            return "wtg2024@"  # 默认值
        return v

    # 协议解析相关配置
    PROTOCOL_PARSE_FOLDER: str = "protocol_parse_files"
    if not os.path.exists(PROTOCOL_PARSE_FOLDER):
        os.makedirs(PROTOCOL_PARSE_FOLDER)


settings = Settings()
