import json
from datetime import datetime

"""
输入：
- 患者病历文件列表
- 审核指标

步骤：
- 数据格式化，将病历数据转化成数组
- 遍历数组，从病历中抽取每个审核指标的检测值，参考值和是否存在风险，以及判断是否符合入选，以及不符合入选的原因
- 结果预处理
- 结果去重与合并「指标名相同项，只取第一个」

输出：
- 审核结果
[
    {
        "collectionId": "69c309b8d7d411efbe160242ac120006",
        "reportName": "医院检验报告单",
        "testResults": [
            {
                "testItemName": "白细胞数",
                "referenceRange": "3.5-9.5",
                "passCriteria": "(5.0-9.0)×10^9/L",
                "testResult": "5.96",
                "passResult": "NOT_PASSED",
                "notPassedReason": "白细胞数5.96小于入组要求最小值5.0",
                "sourceRagIds": ["ad77cea1df09e050"]
            },
            {
                "testItemName": "血小板计数",
                "referenceRange": "125-350",
                "passCriteria": "100x10^9/L-300x10^9/L",
                "testResult": "412",
                "passResult": "PASSED",
                "notPassedReason": "N/A",
                "sourceRagIds": ["ad77cea1df09e050"]
            }
        ]
    },
    {
        "collectionId": "8072c5a4d7d411efb8770242ac120006",
        "reportName": "门(急)诊病历",
        "testResults": [
            {
                "testItemName": "白细胞数",
                "referenceRange": "3.5-9.5",
                "passCriteria": "(5.0-9.0)×10^9/L",
                "testResult": "5.96",
                "passResult": "NOT_PASSED",
                "notPassedReason": "白细胞数5.96小于入组要求最小值5.0",
                "sourceRagIds": ["ad77cea1df09e050"]
            },
            {
                "testItemName": "年龄",
                "referenceRange": "无参考范围",
                "passCriteria": "18-60",
                "testResult": "76岁",
                "passResult": "NOT_PASSED",
                "notPassedReason": "年龄76岁大于入组要求最大值60",
                "sourceRagIds": ["e988a325aa513604"]
            },
            {
                "testItemName": "总胆红素",
                "referenceRange": "无",
                "passCriteria": "＜1.5×ULN",
                "testResult": "无检查结果",
                "passResult": "NOT_FOUND",
                "notPassedReason": "未找到相应指标数据",
                "sourceRagIds": ["e988a325aa513604"]
            },
            {
                "testItemName": "尿素",
                "referenceRange": "无",
                "passCriteria": "3-6",
                "testResult": "无检查结果",
                "passResult": "NOT_FOUND",
                "notPassedReason": "未找到相应指标数据",
                "sourceRagIds": ["e988a325aa513604"]
            }
        ]
    }
]
"""


# 数据预处理
def main(arg1: str, arg2: str) -> dict:
    """
    将 mr_text 和 mr_requirement 转化成 JSON
    :param arg1:
    :param arg2:
    :return:
    """
    mr_doc_list = json.loads(arg1)
    mr_doc_list = [json.dumps(i, ensure_ascii=False, separators=(",", ":")) for i in mr_doc_list]

    return {
        "mr_doc_list": mr_doc_list,
        "mr_requirement_text": json.dumps(json.loads(arg2), ensure_ascii=False, separators=(",", ":")),
    }


# 病历信息预处理
def preprocess_mr_doc(mr_doc: str) -> dict:
    """
    :param mr_doc:
    :return:
    {"ragId": "ad77cea1df09e050", "datasetId": "a5e6be8cd7d211efb78f0242ac120006", "collectionId": "69c309b8d7d411efbe160242ac120006", "content": ""}
    """
    d = json.loads(mr_doc)
    return {
        "datasetId": d["datasetId"],
        "collectionId": d["collectionId"],
        "content": d["content"],
        "ragId": d["ragId"],
    }


# 单病历审核结果后处理
def build_chunk_mr_result(llm_result: str, datasetId: str, collectionId: str, ragId: str) -> dict[str, str]:
    """
    :param llm_result:
    :param datasetId:
    :param collectionId:
    :param ragId:
    :return:
    {
        "result": [
            {
                "datasetId": "",
                "ragId": "",
                "collectionId": "69c309b8d7d411efbe160242ac120006",
                "testItemName": "白细胞数",
                "referenceRange": "3.5-9.5",
                "passCriteria": "(5.0-9.0)×10^9/L",
                "testResult": "5.96",
                "passResult": "NOT_PASSED",
                "notPassedReason": "白细胞数5.96小于入组要求最小值5.0",
                "sourceRagIds": ["ad77cea1df09e050"]
            },
            {
                "datasetId": "",
                "ragId": "",
                "collectionId": "69c309b8d7d411efbe160242ac120006",
                "testItemName": "血小板计数",
                "referenceRange": "125-350",
                "passCriteria": "100x10^9/L-300x10^9/L",
                "testResult": "412",
                "passResult": "PASSED",
                "notPassedReason": "N/A",
                "sourceRagIds": ["ad77cea1df09e050"]
            }
        ]
    }
    """
    mr_list = json.loads(llm_result)
    result = [
        {
            "datasetId": datasetId,
            "collectionId": collectionId,
            "ragId": ragId,
            "testItemNo": i.get("testItemNo"),
            "testItemName": i.get("testItemName"),
            "referenceRange": i.get("referenceRange"),
            "passCriteria": i.get("passCriteria"),
            "testResult": i.get("testResult"),
            "passResult": i.get("passResult"),
            "notPassedReason": i.get("notPassedReason"),
        }
        for i in mr_list
    ]

    return {
        "result": json.dumps(result, ensure_ascii=False, separators=(",", ":"))
    }


# 定义一个排序函数，用于处理 'examTime' 字段
def sort_by_exam_time(item):
    # 如果 examTime 是 N/A, 返回最小日期时间以保证它排在最后
    if item['examTime'] == "N/A":
        return datetime.min
    # 尝试将字符串转换为日期时间对象
    try:
        return datetime.strptime(item['examTime'], "%Y-%m-%d")
    except ValueError:
        # 如果格式不正确，也返回最小日期时间
        return datetime.min


# 审核结果合并，并去重
def merge_mr_result(mr_result_list: list) -> dict:
    result = []
    unique_mr_no = set()

    for i in mr_result_list:
        mr_item_result_list = json.loads(i)
        result += mr_item_result_list

    # 过滤结果为空的结果
    result = [i for i in result if i.get("testResult") and i.get("testResult") != "N/A"]

    # 根据examTime倒排排序，examTime=N/A 排在最后
    sorted_result = sorted(result, key=sort_by_exam_time, reverse=True)

    # 根据 testItemId 去重
    final_result = []
    for i in sorted_result:
        if i.get("testResult") == "N/A":
            continue
        if i.get("testItemId") not in unique_mr_no:
            unique_mr_no.add(i.get("testItemId"))
            final_result.append(i)

    # 根据 testItemId 升序排序
    final_result = sorted(final_result, key=lambda x: x.get("testItemId"))

    return {
        "result": json.dumps(final_result, ensure_ascii=False, separators=(",", ":"))
    }


# 提取病历检测值
def extract_mr_test_results(mr_doc_item: dict, mr_requirement_text: str) -> list:
    """
    :param mr_doc_item:
    {
        "ragId": "ad77cea1df09e050",
        "datasetId": "a5e6be8cd7d211efb78f0242ac120006",
        "collectionId": "69c309b8d7d411efbe160242ac120006",
        "content": "医院检验报告单\n性别：女\n年龄：76岁\n样本编号：406\n科室：\n门诊/住院号：\n病床号：\n送检医师：\n标本种类：静脉血\n备\n注：\n药物临床试验\n临床诊断：\n送检目的：\n项目\n英文缩写\n测定值\n单位\n参考值\n1★白细胞数\nWBC\n5.96\n10 ~ 9/L\n3.5-9.5\n2★红细胞数\nRBC\n4.61\n10~12/L\n3.8-5.1\n3★血红蛋白浓度\nHGB\n139\ng/L\n115-150\n4★红细胞压积\nHCT\n40.50\n% \n35-45\n5平均红细胞体积\nMCV\n87.9\nfL\n82-100\n6平均红细胞血红蛋白含量\n27-34\nMCH\n30.2\npg\n7 平均红细胞血红蛋白浓度\nMCHC\n343\ng/L\n316-354\n8★血小板数\nPLT\n412\n10~9/L\n125-350\n9红细胞体积分布宽度-SD\nRDW-SD\n41.0\n37-54\n10红细胞体积分布宽度-CV\nRDW-CV\n12.3\n11-16\n% \n11血小板体积分布宽度\nPDW\n16. 1\nfL\n9-17\n12 平均血小板体积\nMPV\n8.4\n↓NisfL\n9. 4-12.7\nP-LCR\n15.8\n% \n13-43\n13大血小板比率\n14 血小板压积\nPCT\n0.35\n%\n0.108-0.282\n10 ~ 9/L\n15中性粒细胞绝对值\nNEUT#\n3.86\n1.8-6.3\nLYMPH#\n1. 74\n10 ~9/L\n1.1-3.2\n16淋巴细胞绝对值\nMONO#\n0.29\n10 ~9/L\n17单核细胞绝对值\n0.1-0.6\n18嗜酸性粒细胞绝对值\nEO#\n0.05\n10 ~9/L\n0.02-0.52\n0.02\n10~9/L\n19嗜碱性粒细胞绝对值\nBASO#\n0.00-0.06\n20中性粒细胞百分比\nNEUT%\n64.90\n% \n40-75\n29.20\n% \n20-50\n21淋巴细胞百分比\nLYMPH%\n%\n3-10\n22单核细胞百分比\nMONO%\n4.80\nE0%\n0.80\n% \n0.4-8\n23嗜酸性粒细胞百分比\n% \n0-1\n24嗜碱性粒细胞百分比\nBASO%\n0.30"
    }

    :param mr_requirement_text:
    :return:
    {
        "collectionId": "69c309b8d7d411efbe160242ac120006",
        "reportName": "医院检验报告单",
        "testResults": [
            {
                "testItemNo": "1",
                "testItemName": "白细胞数",
                "referenceRange": "3.5-9.5",
                "passCriteria": "(5.0-9.0)×10^9/L",
                "testResult": "5.96",
                "passResult": "NOT_PASSED",
                "notPassedReason": "白细胞数5.96小于入组要求最小值5.0",
                "sourceRagIds": ["ad77cea1df09e050"]
            },
            {
                "testItemNo": "2",
                "testItemName": "血小板计数",
                "referenceRange": "125-350",
                "passCriteria": "100x10^9/L-300x10^9/L",
                "testResult": "412",
                "passResult": "PASSED",
                "notPassedReason": "N/A",
                "sourceRagIds": ["ad77cea1df09e050"]
            }
        ]
    }
    """
    pass


# 从初审中提取必要信息
def build_review_params(first_review_result: str) -> dict:
    """
    [
        {
            "testItemNo": "1",
            "testItemName": "白细胞",
            "referenceRange": "3.5-9.5",
            "passCriteria": "(5.0-9.0)×10^9/L",
            "testResult": "5.96",
            "passResult": "NOT_PASSED",
            "notPassedReason": "白细胞数5.96小于入组要求最小值5.0"
        },
    ]
    :param first_review_result:
    :return:
    """
    d = json.loads(first_review_result)
    result = [
        {
            "testItemNo": i.get("testItemNo"),
            "testItemName": i.get("testItemName"),
            "referenceRange": i.get("referenceRange"),
            "passCriteria": i.get("passCriteria"),
            "testResult": i.get("testResult"),
            "passResult": i.get("passResult"),
            "notPassedReason": i.get("notPassedReason"),
        }
        for i in d
    ]

    return {
        "result": json.dumps(result, ensure_ascii=False, separators=(",", ":"))
    }


# 返回结果拼接溯源信息
def build_result(arg1: list, arg2: list) -> list:
    # 创建一个字典来映射 arg1 中的 testItemNo 到其索引位置，以便快速查找
    index_map = {item['testItemNo']: i for i, item in enumerate(arg1)}

    # 遍历 arg2 中的每一个元素
    for item2 in arg2:
        # 如果 arg1 中存在相应的 testItemNo
        if item2['testItemNo'] in index_map:
            index = index_map[item2['testItemNo']]
            # 覆盖 passResult 和 notPassedReason
            arg1[index]['passResult'] = item2['passResult']
            arg1[index]['notPassedReason'] = item2['notPassedReason']

    return arg1


def build_result2(arg1: str, arg2: str) -> dict:
    arg1 = json.loads(arg1)
    arg2 = json.loads(arg2)
    return {
        "result": json.dumps(build_result(arg1, arg2), ensure_ascii=False, separators=(",", ":"))
    }


if __name__ == '__main__':
    # data = {
    #     "mr_text": "[\n            {\n                \"ragId\": \"ad77cea1df09e050\",\n                \"datasetId\": \"a5e6be8cd7d211efb78f0242ac120006\",\n                \"collectionId\": \"69c309b8d7d411efbe160242ac120006\",\n                \"content\": \"医院检验报告单\\n性别：女\\n年龄：76岁\\n样本编号：406\\n科室：\\n门诊/住院号：\\n病床号：\\n送检医师：\\n标本种类：静脉血\\n备\\n注：\\n药物临床试验\\n临床诊断：\\n送检目的：\\n项目\\n英文缩写\\n测定值\\n单位\\n参考值\\n1★白细胞数\\nWBC\\n5.96\\n10 ~ 9/L\\n3.5-9.5\\n2★红细胞数\\nRBC\\n4.61\\n10~12/L\\n3.8-5.1\\n3★血红蛋白浓度\\nHGB\\n139\\ng/L\\n115-150\\n4★红细胞压积\\nHCT\\n40.50\\n% \\n35-45\\n5平均红细胞体积\\nMCV\\n87.9\\nfL\\n82-100\\n6平均红细胞血红蛋白含量\\n27-34\\nMCH\\n30.2\\npg\\n7 平均红细胞血红蛋白浓度\\nMCHC\\n343\\ng/L\\n316-354\\n8★血小板数\\nPLT\\n412\\n10~9/L\\n125-350\\n9红细胞体积分布宽度-SD\\nRDW-SD\\n41.0\\n37-54\\n10红细胞体积分布宽度-CV\\nRDW-CV\\n12.3\\n11-16\\n% \\n11血小板体积分布宽度\\nPDW\\n16. 1\\nfL\\n9-17\\n12 平均血小板体积\\nMPV\\n8.4\\n↓NisfL\\n9. 4-12.7\\nP-LCR\\n15.8\\n% \\n13-43\\n13大血小板比率\\n14 血小板压积\\nPCT\\n0.35\\n%\\n0.108-0.282\\n10 ~ 9/L\\n15中性粒细胞绝对值\\nNEUT#\\n3.86\\n1.8-6.3\\nLYMPH#\\n1. 74\\n10 ~9/L\\n1.1-3.2\\n16淋巴细胞绝对值\\nMONO#\\n0.29\\n10 ~9/L\\n17单核细胞绝对值\\n0.1-0.6\\n18嗜酸性粒细胞绝对值\\nEO#\\n0.05\\n10 ~9/L\\n0.02-0.52\\n0.02\\n10~9/L\\n19嗜碱性粒细胞绝对值\\nBASO#\\n0.00-0.06\\n20中性粒细胞百分比\\nNEUT%\\n64.90\\n% \\n40-75\\n29.20\\n% \\n20-50\\n21淋巴细胞百分比\\nLYMPH%\\n%\\n3-10\\n22单核细胞百分比\\nMONO%\\n4.80\\nE0%\\n0.80\\n% \\n0.4-8\\n23嗜酸性粒细胞百分比\\n% \\n0-1\\n24嗜碱性粒细胞百分比\\nBASO%\\n0.30\"\n            },\n            {\n                \"ragId\": \"e988a325aa513604\",\n                \"datasetId\": \"a5e6be8cd7d211efb78f0242ac120006\",\n                \"collectionId\": \"8072c5a4d7d411efb8770242ac120006\",\n                \"content\": \"门(急)诊病历就诊日期：2024-08-28姓名：性别：女性年龄：76岁就诊科室：眼底病眼神经现病史：1、双眼年龄相关性黄斑变性：2021-10-27至今患者视力模糊，于2021-10-27在本院就诊，病史诊断：双眼年2、龄相关性黄斑变性，近期因视力模糊到本院就诊，确诊双眼年龄相关性黄斑变性。\"\n            },\n            {\n                \"ragId\": \"2a58d520b99347e3\",\n                \"datasetId\": \"a5e6be8cd7d211efb78f0242ac120006\",\n                \"collectionId\": \"8072c5a4d7d411efb8770242ac120006\",\n                \"content\": \"知快3、程：患今日到院就诊，根据患者目前的情况，考虑可能符合本科室开展的诊向患者评细外绍丁试验万案中的试验概况、研究目的、研究方法、试验流程、研究期限、受试者的权利及风险受益、可能获得的补助、参加试验预期的花费、自愿性、参加试验预计的人数等内容。患者充分考虑后未提出问题。患者表示已经理解此次临床试验过程中可能产生的风险和获益，且自愿参加此临床试验，同意在整个试验过程中按照试验方案要求进行试验，并清楚知道参加是自愿的，可以在任何时候退出试验而不会因此受到惩罚或影响后续治疗。患者本人于2024年08月28日09时00分签署知情同意书(版本号：V2.0, 版本日期：2024年05月11日),一份交由患者自行保管，一份保存于受试\"\n            },\n            {\n                \"ragId\": \"39f2fde52d934a4c\",\n                \"datasetId\": \"a5e6be8cd7d211efb78f0242ac120006\",\n                \"collectionId\": \"8072c5a4d7d411efb8770242ac120006\",\n                \"content\": \"者文件夹内。分配受试者筛选号：相关检查。于今日开始进行筛选期既往病史：4、高血压开始时间2014-UK-UK至今给予药物治疗，控制良好高脂血症开始时间2021-09-UK至今未治疗腰肌劳损开始时间1980-UK-UK至今未治疗双眼干眼症开始时间2023-03-09至今未治疗右眼玻璃膜疣开始时间2021-10-27至今未治疗\"\n            },\n            {\n                \"ragId\": \"28320cd51d473409\",\n                \"datasetId\": \"a5e6be8cd7d211efb78f0242ac120006\",\n                \"collectionId\": \"8072c5a4d7d411efb8770242ac120006\",\n                \"content\": \"<table>\\n<tr><td  >姓名：</td><td  >别：</td><td  >年 龄：76岁样本编号</td><td  >：406</td><td></td><td></td></tr>\\n<tr><td  >科室：</td><td  >性门诊/住院号：</td><td  >病床号：</td><td  >送检医师：</td><td></td><td></td></tr>\\n<tr><td  >标本种类：尿液</td><td  >备注：</td><td  >药物临床试验</td><td></td><td></td><td></td></tr>\\n<tr><td  >临床诊断：</td><td></td><td></td><td></td><td></td><td></td></tr>\\n<tr><td  >送检目的：</td><td></td><td></td><td></td><td></td><td></td></tr>\\n<tr><td  >项目</td><td></td><td  >英文缩写 测定值</td><td></td><td  >单位</td><td  >参考值</td></tr>\\n<tr><td  >1  葡萄糖</td><td  >GLU</td><td  >Negative</td><td></td><td></td><td  >阴性</td></tr>\\n<tr><td  >2 维生素C </td><td  >Vc</td><td  >Negative</td><td></td><td></td><td  >阴性</td></tr>\\n<tr><td  >3 蛋白</td><td  >PRO</td><td></td><td></td><td></td><td></td></tr>\\n<tr><td></td><td></td><td  >Negative</td><td></td><td></td><td  >阴性</td></tr>\\n<tr><td  >4 酮体</td><td  >KET</td><td  >Negative</td><td></td><td></td><td  >阴性</td></tr>\\n<tr><td  >5 酸碱度 6 比重</td><td  >PH SG</td><td  >6.5 1.015</td><td></td><td></td><td  >4.5-8 1.002-1.030</td></tr>\\n<tr><td  >7 胆红 素</td><td  >BIL</td><td  >Negative</td><td></td><td></td><td  >阴性</td></tr>\\n<tr><td  >8 亚硝酸盐 9 尿胆原 11红细胞LEU </td><td  >NIT URO 10白细胞 BLO </td><td  >Negative 正常 Negative Negative</td><td></td><td  >EU/dl </td><td  >阴性 0-1.56 阴性 阴性</td></tr>\\n</table>\"\n            },\n            {\n                \"ragId\": \"c1a19997d9ff8daa\",\n                \"datasetId\": \"a5e6be8cd7d211efb78f0242ac120006\",\n                \"collectionId\": \"8072c5a4d7d411efb8770242ac120006\",\n                \"content\": \"(该门诊病历内容及含义的最终解释权归第1页/共4页验报检采样时间：2024-08-28 09:28 检验时间：2024-08-28 09:28审核时间：2024-08-28 09:35打印时间：2024-08-28 09:40门(急)诊病历就诊日期：2024-08-28性别：女性年龄：76岁就诊科室：眼底病眼神经\"\n            },\n            {\n                \"ragId\": \"285a6f980cc9a462\",\n                \"datasetId\": \"a5e6be8cd7d211efb78f0242ac120006\",\n                \"collectionId\": \"8072c5a4d7d411efb8770242ac120006\",\n                \"content\": \"姓名。血小板数升高开始时间2024-08-28 Y-谷氨酰转肽酶开始时间2024-08-28右眼老年性白内障开始时间2021-10-20 结束时间2022-06-01 手术治疗左眼老年性白内障开始时间2021-10-20 结束时间2023-02-10手术治疗冠心病开始时间2021-UK-UK药物治疗\"\n            },\n            {\n                \"ragId\": \"0e47bde676245ea2\",\n                \"datasetId\": \"a5e6be8cd7d211efb78f0242ac120006\",\n                \"collectionId\": \"8072c5a4d7d411efb8770242ac120006\",\n                \"content\": \"治疗史：5、2021-11-08、2021-12-08、2022-01-05、2022-02-28、2022-03-30、2022-04-27、2022-05-25、2022-06-22、2022-07-20、2022-08-17、2022-09-14、2023-05-16、2023-06-13、2023-08-08、2023-09-05、2024-05-28、2024-07-02、2024-04-16、2024-03-05、2024-01-09分别注射试验用药物具体药量不详左眼玻璃体腔内注药用药目的\"\n            },\n            {\n                \"ragId\": \"32e3c15bb2588387\",\n                \"datasetId\": \"a5e6be8cd7d211efb78f0242ac120006\",\n                \"collectionId\": \"8072c5a4d7d411efb8770242ac120006\",\n                \"content\": \"左眼AMD。雷珠单抗眼内注射液用量0.2ml 左眼玻璃体腔注药用药目的：左眼AMD 开始时间2023-10-31 结束时间2023-10-31雷珠单抗眼内注射液用量0.2ml 左眼玻璃体腔注药用药目的：左眼AMD开始时间2023-12-05 结束时间2023-12-05\"\n            },\n            {\n                \"ragId\": \"6535a22e54a9cbd3\",\n                \"datasetId\": \"a5e6be8cd7d211efb78f0242ac120006\",\n                \"collectionId\": \"8072c5a4d7d411efb8770242ac120006\",\n                \"content\": \"手术史：6、右眼白内障超声乳化联合人工晶体植入术：开始时间2022-06-01结束时间2022-06-01左眼白内障超声乳化联合人工晶体植入术：开始时间2023-02-10结束时间2023-02-10家族史：无7、绝经史：2000年绝经8、用药史：9、氯化钠注射液40ml 双冲眼开始时间2023-10-31 结束时间2023-10-31用药目的：术前用药\"\n            },\n            {\n                \"ragId\": \"d33ec5b6bf2dc329\",\n                \"datasetId\": \"a5e6be8cd7d211efb78f0242ac120006\",\n                \"collectionId\": \"8072c5a4d7d411efb8770242ac120006\",\n                \"content\": \"氧氟沙星滴眼液0.4ml 双点眼开始时间2023-10-31 结束时间024-08-28(该门诊病历内容及含义的最终解释第2页/共4页\"\n            }\n        ]",
    #     "mr_requirement": "[\n   {\n       \"testItemName\": \"白细胞\",\n       \"testItemNo\": \"1\",\n       \"passCriteria\": \"(5.0-9.0)×10^9/L\"\n   },\n   {\n       \"testItemName\": \"血小板计数\",\n       \"testItemNo\": \"2\",\n       \"passCriteria\": \"100x10^9/L-300x10^9/L\"\n   },\n   {\n       \"testItemName\": \"年龄\",\n       \"testItemNo\": \"3\",\n       \"passCriteria\": \"18-60\"\n   },\n   {\n       \"testItemName\": \"总胆红素\",\n       \"testItemNo\": \"4\",\n       \"passCriteria\": \"＜1.5×ULN\"\n   },\n   {\n       \"testItemName\": \"尿素\",\n       \"testItemNo\": \"5\",\n       \"passCriteria\": \"3-6\"\n   }\n]",
    #     "sys.files": [],
    #     "sys.user_id": "d5703770-b92c-41a6-adfa-32c14f32e94a",
    #     "sys.app_id": "af9f46d4-ab95-4370-93a3-3e1dfe8c7c88",
    #     "sys.workflow_id": "06a1485b-055b-42dc-a838-46b00b8e0bde",
    #     "sys.workflow_run_id": "7299af00-2cfe-4e62-be3c-2411de7644ce"
    # }
    #
    # print(json.dumps(main(data.get("mr_text"), data.get("mr_requirement")), ensure_ascii=False))
    # print(data.get("mr_text"))
    # print(data.get("mr_text"))
    # print("****"*10)
    # print(data.get("mr_requirement"))

    # 构建单个chunk的检测结果
    # d = {
    #     "llm_result": "{\n    \"testItemName\": \"年龄\",\n    \"testItemNo\": \"3\",\n    \"referenceRange\": \"无参考范围\",\n    \"passCriteria\": \"18-60\",\n    \"testResult\": \"76岁\",\n    \"passResult\": \"NOT_PASSED\",\n    \"notPassedReason\": \"年龄76岁超出入组要求范围18-60岁\"\n}",
    #     "datasetId": "a5e6be8cd7d211efb78f0242ac120006",
    #     "collectionId": "8072c5a4d7d411efb8770242ac120006",
    #     "ragId": "28320cd51d473409"
    # }
    # print(build_chunk_mr_result(d.get("llm_result"), d.get("datasetId"), d.get("collectionId"), d.get("ragId")))

    # 合并去重
    # d = {
    #     "mr_result_list": [
    #         "[{\"datasetId\":\"a5e6be8cd7d211efb78f0242ac120006\",\"collectionId\":\"69c309b8d7d411efbe160242ac120006\",\"ragId\":\"ad77cea1df09e050\",\"testItemNo\":\"1\",\"testItemName\":\"白细胞\",\"referenceRange\":\"3.5-9.5\",\"passCriteria\":\"(5.0-9.0)×10^9/L\",\"testResult\":\"5.96\",\"passResult\":\"NOT_PASSED\",\"notPassedReason\":\"白细胞数5.96小于入组要求最小值5.0\"},{\"datasetId\":\"a5e6be8cd7d211efb78f0242ac120006\",\"collectionId\":\"69c309b8d7d411efbe160242ac120006\",\"ragId\":\"ad77cea1df09e050\",\"testItemNo\":\"2\",\"testItemName\":\"血小板计数\",\"referenceRange\":\"125-350\",\"passCriteria\":\"100x10^9/L-300x10^9/L\",\"testResult\":\"412\",\"passResult\":\"NOT_PASSED\",\"notPassedReason\":\"血小板计数412高于入组要求最大值300\"},{\"datasetId\":\"a5e6be8cd7d211efb78f0242ac120006\",\"collectionId\":\"69c309b8d7d411efbe160242ac120006\",\"ragId\":\"ad77cea1df09e050\",\"testItemNo\":\"3\",\"testItemName\":\"年龄\",\"referenceRange\":\"无参考范围\",\"passCriteria\":\"18-60\",\"testResult\":\"76岁\",\"passResult\":\"NOT_PASSED\",\"notPassedReason\":\"年龄76岁超出入组要求最大值60岁\"}]",
    #         "[{\"datasetId\":\"a5e6be8cd7d211efb78f0242ac120006\",\"collectionId\":\"8072c5a4d7d411efb8770242ac120006\",\"ragId\":\"e988a325aa513604\",\"testItemNo\":\"3\",\"testItemName\":\"年龄\",\"referenceRange\":\"无参考范围\",\"passCriteria\":\"18-60\",\"testResult\":\"76岁\",\"passResult\":\"NOT_PASSED\",\"notPassedReason\":\"年龄76岁超出入组要求最大值60岁\"}]",
    #         "[]",
    #         "[]",
    #         "[{\"datasetId\":\"a5e6be8cd7d211efb78f0242ac120006\",\"collectionId\":\"8072c5a4d7d411efb8770242ac120006\",\"ragId\":\"28320cd51d473409\",\"testItemNo\":\"3\",\"testItemName\":\"年龄\",\"referenceRange\":\"无参考范围\",\"passCriteria\":\"18-60\",\"testResult\":\"76岁\",\"passResult\":\"NOT_PASSED\",\"notPassedReason\":\"年龄76岁超出入组要求范围18-60岁\"}]",
    #         "[{\"datasetId\":\"a5e6be8cd7d211efb78f0242ac120006\",\"collectionId\":\"8072c5a4d7d411efb8770242ac120006\",\"ragId\":\"c1a19997d9ff8daa\",\"testItemNo\":\"3\",\"testItemName\":\"年龄\",\"referenceRange\":\"无参考范围\",\"passCriteria\":\"18-60\",\"testResult\":\"76岁\",\"passResult\":\"NOT_PASSED\",\"notPassedReason\":\"年龄76岁超出入组要求范围18-60岁\"}]",
    #         "[{\"datasetId\":\"a5e6be8cd7d211efb78f0242ac120006\",\"collectionId\":\"8072c5a4d7d411efb8770242ac120006\",\"ragId\":\"285a6f980cc9a462\",\"testItemNo\":\"2\",\"testItemName\":\"血小板计数\",\"referenceRange\":\"125-350\",\"passCriteria\":\"100x10^9/L-300x10^9/L\",\"testResult\":\"N/A\",\"passResult\":\"NOT_PASSED\",\"notPassedReason\":\"病历中未提供血小板计数结果\"},{\"datasetId\":\"a5e6be8cd7d211efb78f0242ac120006\",\"collectionId\":\"8072c5a4d7d411efb8770242ac120006\",\"ragId\":\"285a6f980cc9a462\",\"testItemNo\":\"3\",\"testItemName\":\"年龄\",\"referenceRange\":\"无参考范围\",\"passCriteria\":\"18-60\",\"testResult\":\"N/A\",\"passResult\":\"NOT_PASSED\",\"notPassedReason\":\"病历中未提供年龄信息\"},{\"datasetId\":\"a5e6be8cd7d211efb78f0242ac120006\",\"collectionId\":\"8072c5a4d7d411efb8770242ac120006\",\"ragId\":\"285a6f980cc9a462\",\"testItemNo\":\"4\",\"testItemName\":\"总胆红素\",\"referenceRange\":\"无参考范围\",\"passCriteria\":\"＜1.5×ULN\",\"testResult\":\"N/A\",\"passResult\":\"NOT_PASSED\",\"notPassedReason\":\"病历中未提供总胆红素结果\"},{\"datasetId\":\"a5e6be8cd7d211efb78f0242ac120006\",\"collectionId\":\"8072c5a4d7d411efb8770242ac120006\",\"ragId\":\"285a6f980cc9a462\",\"testItemNo\":\"5\",\"testItemName\":\"尿素\",\"referenceRange\":\"无参考范围\",\"passCriteria\":\"3-6\",\"testResult\":\"N/A\",\"passResult\":\"NOT_PASSED\",\"notPassedReason\":\"病历中未提供尿素结果\"}]",
    #         "[]",
    #         "[]",
    #         "[]",
    #         "[]"
    #     ]
    # }
    #
    # print(merge_mr_result(d.get("mr_result_list")).get("result"))

    data = {
        "mr_result_list": [
            "[{\"datasetId\":\"a5e6be8cd7d211efb78f0242ac120006\",\"collectionId\":\"69c309b8d7d411efbe160242ac120006\",\"ragId\":\"ad77cea1df09e050\",\"testItemId\":\"1\",\"testItemName\":\"白细胞\",\"referenceRange\":\"3.5-9.5\",\"passCriteria\":\"(5.0-9.0)×10^9/L\",\"testResult\":\"5.96\",\"passResult\":\"NOT_PASSED\",\"notPassedReason\":\"白细胞数5.96小于入组要求最小值5.0\",\"examTime\":\"2024-01-01\"},{\"datasetId\":\"a5e6be8cd7d211efb78f0242ac120006\",\"collectionId\":\"69c309b8d7d411efbe160242ac120006\",\"ragId\":\"ad77cea1df09e050\",\"testItemId\":\"2\",\"testItemName\":\"血小板计数\",\"referenceRange\":\"125-350\",\"passCriteria\":\"100x10^9/L-300x10^9/L\",\"testResult\":\"412\",\"passResult\":\"PASSED\",\"notPassedReason\":\"N/A\",\"examTime\":\"2024-01-01\"},{\"datasetId\":\"a5e6be8cd7d211efb78f0242ac120006\",\"collectionId\":\"69c309b8d7d411efbe160242ac120006\",\"ragId\":\"ad77cea1df09e050\",\"testItemId\":\"3\",\"testItemName\":\"年龄\",\"referenceRange\":\"无参考范围\",\"passCriteria\":\"18-60\",\"testResult\":\"76岁\",\"passResult\":\"NOT_PASSED\",\"notPassedReason\":\"年龄76岁超出入组要求范围18-60\",\"examTime\":\"2024-01-01\"}]",
            "[{\"datasetId\":\"a5e6be8cd7d211efb78f0242ac120006\",\"collectionId\":\"8072c5a4d7d411efb8770242ac120006\",\"ragId\":\"e988a325aa513604\",\"testItemId\":\"3\",\"testItemName\":\"年龄\",\"referenceRange\":\"无参考范围\",\"passCriteria\":\"18-60\",\"testResult\":\"76岁\",\"passResult\":\"NOT_PASSED\",\"notPassedReason\":\"年龄76岁超出入组要求范围18-60\",\"examTime\":\"2024-08-28\"}]",
            "[]",
            "[{\"datasetId\":\"a5e6be8cd7d211efb78f0242ac120006\",\"collectionId\":\"8072c5a4d7d411efb8770242ac120006\",\"ragId\":\"39f2fde52d934a4c\",\"testItemId\":\"1\",\"testItemName\":\"白细胞\",\"referenceRange\":\"(5.0-9.0)×10^9/L\",\"passCriteria\":\"(5.0-9.0)×10^9/L\",\"testResult\":\"N/A\",\"passResult\":\"NOT_PASSED\",\"notPassedReason\":\"病历中未找到白细胞检查结果\",\"examTime\":\"N/A\"},{\"datasetId\":\"a5e6be8cd7d211efb78f0242ac120006\",\"collectionId\":\"8072c5a4d7d411efb8770242ac120006\",\"ragId\":\"39f2fde52d934a4c\",\"testItemId\":\"2\",\"testItemName\":\"血小板计数\",\"referenceRange\":\"100x10^9/L-300x10^9/L\",\"passCriteria\":\"100x10^9/L-300x10^9/L\",\"testResult\":\"N/A\",\"passResult\":\"NOT_PASSED\",\"notPassedReason\":\"病历中未找到血小板计数检查结果\",\"examTime\":\"N/A\"},{\"datasetId\":\"a5e6be8cd7d211efb78f0242ac120006\",\"collectionId\":\"8072c5a4d7d411efb8770242ac120006\",\"ragId\":\"39f2fde52d934a4c\",\"testItemId\":\"3\",\"testItemName\":\"年龄\",\"referenceRange\":\"18-60\",\"passCriteria\":\"18-60\",\"testResult\":\"76岁\",\"passResult\":\"NOT_PASSED\",\"notPassedReason\":\"年龄76岁超出入组要求范围18-60\",\"examTime\":\"N/A\"},{\"datasetId\":\"a5e6be8cd7d211efb78f0242ac120006\",\"collectionId\":\"8072c5a4d7d411efb8770242ac120006\",\"ragId\":\"39f2fde52d934a4c\",\"testItemId\":\"4\",\"testItemName\":\"总胆红素\",\"referenceRange\":\"＜1.5×ULN\",\"passCriteria\":\"＜1.5×ULN\",\"testResult\":\"N/A\",\"passResult\":\"NOT_PASSED\",\"notPassedReason\":\"病历中未找到总胆红素检查结果\",\"examTime\":\"N/A\"},{\"datasetId\":\"a5e6be8cd7d211efb78f0242ac120006\",\"collectionId\":\"8072c5a4d7d411efb8770242ac120006\",\"ragId\":\"39f2fde52d934a4c\",\"testItemId\":\"5\",\"testItemName\":\"尿素\",\"referenceRange\":\"3-6\",\"passCriteria\":\"3-6\",\"testResult\":\"N/A\",\"passResult\":\"NOT_PASSED\",\"notPassedReason\":\"病历中未找到尿素检查结果\",\"examTime\":\"N/A\"}]",
            "[{\"datasetId\":\"a5e6be8cd7d211efb78f0242ac120006\",\"collectionId\":\"8072c5a4d7d411efb8770242ac120006\",\"ragId\":\"28320cd51d473409\",\"testItemId\":\"3\",\"testItemName\":\"年龄\",\"referenceRange\":\"无参考范围\",\"passCriteria\":\"18-60\",\"testResult\":\"76岁\",\"passResult\":\"NOT_PASSED\",\"notPassedReason\":\"年龄76岁超出入组要求18-60岁\",\"examTime\":\"N/A\"}]"
        ]
    }
    result = merge_mr_result(data.get("mr_result_list"))
    print(result)
