import requests

url = "http://localhost:8317/v1/chat/completions"
token = "sk-AIzaSyCCKk9MkXfyBVc-ap61XUrNm3s-kDlT5is"

headers = {
    "Authorization": f"Bearer {token}",
    "Content-Type": "application/json"
}

payload = {
    "model": "gemini-2.5-pro",
    "messages": [
        {"role": "user", "content": "Hello, how are you?"}
    ]
}

response = requests.post(url, headers=headers, json=payload)

print("Status Code:", response.status_code)
print("Headers:", response.headers)
print("Raw Response:", response.text)