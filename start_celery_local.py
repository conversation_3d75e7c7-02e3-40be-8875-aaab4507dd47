#!/usr/bin/env python3
"""
本地启动 Celery Worker 的脚本
"""
import os
import sys
import subprocess
import signal
import time

def start_celery_worker():
    """启动 Celery Worker"""
    print("🚀 启动 Celery Worker...")
    
    # 设置环境变量，禁用 Nacos 避免本地启动问题
    env = os.environ.copy()
    env['DISABLE_NACOS'] = 'true'
    env['NACOS_ENABLE_IN_CELERY'] = 'false'
    
    # Celery 启动命令
    cmd = [
        sys.executable, '-m', 'celery',
        '-A', 'celery_task',
        'worker',
        '--loglevel=info',
        '--pool=prefork',
        '--concurrency=4',
        '--time-limit=300',
        '--soft-time-limit=240',
        '--max-tasks-per-child=20',
        '--prefetch-multiplier=1',
        '--without-gossip',
        '--without-mingle',
        '--without-heartbeat'
    ]
    
    try:
        # 启动 Celery Worker
        process = subprocess.Popen(
            cmd,
            env=env,
            stdout=subprocess.PIPE,
            stderr=subprocess.STDOUT,
            universal_newlines=True,
            bufsize=1
        )
        
        print(f"✅ Celery Worker 已启动，PID: {process.pid}")
        print("📝 日志输出:")
        print("-" * 50)
        
        # 实时输出日志
        for line in process.stdout:
            print(line.rstrip())
            
    except KeyboardInterrupt:
        print("\n🛑 收到中断信号，正在停止 Celery Worker...")
        if 'process' in locals():
            process.terminate()
            try:
                process.wait(timeout=10)
                print("✅ Celery Worker 已停止")
            except subprocess.TimeoutExpired:
                print("⚠️ 强制终止 Celery Worker...")
                process.kill()
                process.wait()
    except Exception as e:
        print(f"❌ 启动失败: {e}")
        return 1
    
    return 0

if __name__ == "__main__":
    sys.exit(start_celery_worker())

