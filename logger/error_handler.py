"""
错误处理工具模块
提供统一的错误处理和日志记录功能
"""

import traceback
from typing import Any, Dict, Optional
from logger.logger import app_logger


class ErrorAnalyzer:
    """错误分析器，用于分析异常链并提取根因"""
    
    @staticmethod
    def analyze_exception_chain(e: Exception) -> Dict[str, Any]:
        """
        分析异常链，提取根因和相关信息
        
        Args:
            e: 异常对象
            
        Returns:
            包含异常分析结果的字典
        """
        # 获取完整的异常链
        tb_str = traceback.format_exc()
        
        # 分析异常类型
        error_type = type(e).__name__
        error_message = str(e)
        
        # 尝试从异常链中提取根因
        root_cause = ErrorAnalyzer._extract_root_cause(tb_str)
        
        # 分析错误类别
        error_category = ErrorAnalyzer._categorize_error(error_message, tb_str)
        
        return {
            "error_type": error_type,
            "error_message": error_message,
            "root_cause": root_cause,
            "error_category": error_category,
            "full_traceback": tb_str,
            "user_message": ErrorAnalyzer._get_user_friendly_message(error_category, root_cause)
        }
    
    @staticmethod
    def _extract_root_cause(tb_str: str) -> Optional[str]:
        """从异常链中提取根因"""
        lines = tb_str.split('\n')
        
        # 查找 ModuleNotFoundError 或 ImportError
        for line in lines:
            if 'ModuleNotFoundError:' in line or 'ImportError:' in line:
                return line.strip()
        
        # 查找其他常见的根因错误
        for line in lines:
            if any(keyword in line for keyword in ['ConnectionError:', 'TimeoutError:', 'PermissionError:']):
                return line.strip()
        
        return None
    
    @staticmethod
    def _categorize_error(error_message: str, tb_str: str) -> str:
        """对错误进行分类"""
        error_lower = error_message.lower()
        tb_lower = tb_str.lower()
        
        if 'pydantic_settings' in tb_lower or 'no module named' in tb_lower:
            return "MISSING_DEPENDENCY"
        elif any(keyword in error_lower for keyword in ['redis', 'connection', 'timeout']):
            return "CONNECTION_ERROR"
        elif 'celeryconfig' in error_lower:
            return "CONFIGURATION_ERROR"
        elif 'permission' in error_lower or 'unauthorized' in error_lower:
            return "PERMISSION_ERROR"
        else:
            return "UNKNOWN_ERROR"
    
    @staticmethod
    def _get_user_friendly_message(error_category: str, root_cause: Optional[str]) -> str:
        """根据错误类别返回用户友好的消息"""
        messages = {
            "MISSING_DEPENDENCY": "系统依赖缺失，请联系技术支持",
            "CONNECTION_ERROR": "网络连接异常，请稍后重试",
            "CONFIGURATION_ERROR": "服务配置异常，请联系技术支持",
            "PERMISSION_ERROR": "权限不足，请联系管理员",
            "UNKNOWN_ERROR": "处理请求时发生错误，请联系技术支持"
        }
        
        return messages.get(error_category, messages["UNKNOWN_ERROR"])


def log_and_handle_error(e: Exception, context: Dict[str, Any], logger=None) -> Dict[str, Any]:
    """
    统一的错误记录和处理函数 - 适用于 API 端点的最外层异常处理
    
    Args:
        e: 异常对象
        context: 上下文信息（如请求参数等）
        logger: 日志记录器，默认使用 app_logger
        
    Returns:
        错误分析结果
    """
    if logger is None:
        logger = app_logger
    
    # 分析异常
    analysis = ErrorAnalyzer.analyze_exception_chain(e)
    
    # 记录详细的错误信息
    logger.error(
        f"错误发生 - 类型: {analysis['error_type']}, "
        f"消息: {analysis['error_message']}, "
        f"根因: {analysis['root_cause']}, "
        f"类别: {analysis['error_category']}, "
        f"上下文: {context}"
    )
    
    # 记录完整的异常信息（使用 Loguru 的正确方式）
    logger.exception("完整异常信息:")
    
    return analysis


def log_simple_error(e: Exception, context_msg: str, logger=None) -> None:
    """
    简单的错误记录函数 - 适用于内部函数的异常处理
    
    Args:
        e: 异常对象
        context_msg: 上下文描述信息
        logger: 日志记录器，默认使用 app_logger
    """
    if logger is None:
        logger = app_logger
    
    # 使用 Loguru 的正确方式记录异常
    logger.exception(f"{context_msg}: {e}")
