import contextvars
import logging
import os

from loguru import logger

from config.env_config import LOG_HOME

request_id = contextvars.ContextVar("request_id", default="")


def get_request_id():
    return request_id.get()


def conf_logger():
    # 配置第三方库的日志级别，减少DEBUG日志输出
    logging.getLogger("oss2").setLevel(logging.WARNING)  # OSS SDK
    logging.getLogger("urllib3").setLevel(logging.WARNING)  # HTTP库
    logging.getLogger("requests").setLevel(logging.WARNING)  # Requests库
    logging.getLogger("celery").setLevel(logging.INFO)  # Celery保持INFO级别
    logging.getLogger("nacos.client").setLevel(logging.INFO)
    logger.info(os.environ)

    logger.add(
        os.path.join(LOG_HOME, "app.log"),
        format="{time:YYYY-MM-DD HH:mm:ss} | {level} | {extra[request_id]} | {message}",
        level="INFO",
        rotation="00:00",
        retention="30 days",
        enqueue=True,
        encoding="utf-8",
        filter=lambda record: record["extra"]["log_tag"] == "app",
    )

    logger.add(
        os.path.join(LOG_HOME, "biz.log"),
        format="\u0001".join(["{time:YYYY-MM-DD HH:mm:ss}", "{level}", "{extra[request_id]}", "{message}"]),
        level="INFO",
        rotation="00:00",
        retention="30 days",
        enqueue=True,
        encoding="utf-8",
        filter=lambda record: record["extra"]["log_tag"] == "biz",
    )

    app_logger = logger.bind(log_tag="app").patch(lambda record: record["extra"].update(request_id=get_request_id()))
    mt_logger = logger.bind(log_tag="biz").patch(lambda record: record["extra"].update(request_id=get_request_id()))
    return app_logger, mt_logger


app_logger, biz_logger = conf_logger()

if __name__ == "__main__":
    app_logger.info("Hello App")
    biz_logger.info("Hello MT")
