import atexit
import json
import os
import multiprocessing
import threading
import time
from typing import Dict

import nacos

from configurer.singleton import singleton
from logger.logger import app_logger

init_configs = {"lamma_parse_config": "{}", "asr_config": "{}", "yiya-gateway-config": "{}", "llm-vl-config": "{}"}

config: Dict[str, Dict] = {}

def init_nacos_manager():
    """
    统一初始化 NacosManager连接入口
    :return: NacosManager instance
    """
    nacos_server = os.environ.get("NACOS_SERVER", "").strip()
    nacos_namespace = os.environ.get("NACOS_NAMESPACE", "").strip()

    # 如果环境变量为空，使用默认值
    if not nacos_server:
        nacos_server = "mse-450596b0-p.nacos-ans.mse.aliyuncs.com:8848"
    if not nacos_namespace:
        nacos_namespace = "yy-prod"

    ak = "LTAI5tSMtzYQ5GVPCm8njDYp"
    sk = "******************************"
    return NacosManager(nacos_server, nacos_namespace, ak, sk, "")

def watcher(args):
    data_id = args["data_id"]
    content = args["content"]
    try:
        data = json.loads(content)
        config[data_id] = data
        app_logger.info(f"配置变更：{data_id} => {content}")
    except Exception as e:
        app_logger.error(f"配置解析异常发生错误:{data_id} => {content}, {e}")
        app_logger.error(f"详细信息: {e.args}")

@singleton
class NacosManager:
    _lock = threading.Lock()

    def __init__(self, server_addresses, namespace=None, username=None, password=None, group_id=None):
        self.group_id = group_id or ""
        self.server_addresses = server_addresses
        self.namespace = namespace
        self.username = username
        self.password = password
        self._client: nacos.NacosClient | None = None
        self._watchers = {}
        self._inited_pid = None  # 记录初始化在哪个进程

    def init_client(self) -> nacos.NacosClient:
        current_pid = multiprocessing.current_process().pid
        if self._client and self._inited_pid == current_pid:
            return self._client

        with self._lock:
            if not self._client  or self._inited_pid != current_pid:
                app_logger.info(f"初始化 NacosManager: server={self.server_addresses}, namespace={self.namespace}")
                self._client = nacos.NacosClient(
                    server_addresses = self.server_addresses,
                    namespace = self.namespace,
                    ak = self.username,
                    sk = self.password
                )
                self._inited_pid = current_pid
                atexit.register(self.stop)
                app_logger.info("完成初始化 NacosManager")

        # 加载初始配置 + 注册监听
        for data_id, default_content in init_configs.items():
            data = self._client.get_config(data_id, self.group_id)
            if data:
                config[data_id] = json.loads(data)
            else:
                data = self._client.get_config(data_id, "DEFAULT_CONFIG")
                if data:
                    config[data_id] = json.loads(data)
                else:
                    config[data_id] = json.loads(default_content)
            self._client.add_config_watcher(data_id, self.group_id, watcher)
            self._watchers[(data_id, self.group_id)] = watcher

        return self._client

    def stop(self):
        """关闭 NacosClient"""
        if self._client:
            try:
                # 取消订阅
                if hasattr(self._client, "_cancel_subscribe"):
                    self._client._cancel_subscribe()
                if hasattr(self._client, "stop_subscribe"):
                    self._client.stop_subscribe()

                # 停止拉取进程
                if hasattr(self._client, "_pulling_process"):
                    proc = self._client._pulling_process
                    if proc.is_alive():
                        proc.terminate()
                        proc.join(timeout=2)

                # 关闭 urllib3 连接池，防止 ResourceWarning
                if hasattr(self._client, "http_client"):
                    try:
                        self._client.http_client.clear()
                        self._client.http_client.pool.clear()
                        self._client.http_client.connection_pool_kw.clear()
                        if hasattr(self._client.http_client, "close"):
                            self._client.http_client.close()
                        app_logger.info("NacosManager http_client 已关闭")
                    except Exception as e:
                        app_logger.warning(f"http_client 关闭失败: {e}")

                # 清理监听器
                self._watchers.clear()
                app_logger.info("NacosManager stop successfully")
            except Exception as e:
                app_logger.warning(f"NacosManager stop failed: {e}")
            finally:
                self._client = None
                self._inited_pid = None

if __name__ == "__main__":
    init_nacos_manager().init_client()
    d = config.get("lamma_parse_config")
    asr = config.get("asr_config")
    dd = config.get("yiya-gateway-config")
    vl = config.get("llm-vl-config")
    print(d)
    print(asr)
    print(dd)
    print(vl)
    time.sleep(1000)
