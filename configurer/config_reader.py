import os
import sys

# 添加项目的父目录到sys.path中，以便Python能找到子目录中的包
project_root = os.path.abspath(os.path.join(os.path.dirname(__file__), ".."))
sys.path.insert(0, project_root)

import os

from configurer.yy_nacos import config

# 默认配置，当 Nacos 配置未加载时使用
DEFAULT_CONFIGS = {
    "lamma_parse_config": {"api_key": "llx-x76NZdc5MqH9zM2bWBSmKkTigfLrKlK47GFeZ5BvLr91eYgf", "premium": False},
    "asr_config": {},
    "yiya-gateway-config": {},
    "llm-vl-config": {},
}


def get_lamma_index_config():
    """获取 LlamaIndex 配置，支持 Nacos 配置未加载的情况"""
    try:
        d = config.get("lamma_parse_config")
        if d:
            return d
    except (KeyError, AttributeError):
        pass

    # 如果 Nacos 配置未加载，使用默认配置
    default_config = DEFAULT_CONFIGS["lamma_parse_config"]
    print(f"⚠️ 使用默认 lamma_parse_config: {default_config}")
    return default_config


def get_asr_config():
    """获取 ASR 配置，支持 Nacos 配置未加载的情况"""
    try:
        d = config.get("asr_config")
        if d:
            return d
    except (KeyError, AttributeError):
        pass

    # 如果 Nacos 配置未加载，使用默认配置
    default_config = DEFAULT_CONFIGS["asr_config"]
    print(f"⚠️ 使用默认 asr_config: {default_config}")
    return default_config


def get_gate_config():
    """获取网关配置，支持 Nacos 配置未加载的情况"""
    try:
        d = config.get("yiya-gateway-config")
        if d:
            return d
    except (KeyError, AttributeError):
        pass

    # 如果 Nacos 配置未加载，使用默认配置
    default_config = DEFAULT_CONFIGS["yiya-gateway-config"]
    print(f"⚠️ 使用默认 yiya-gateway-config: {default_config}")
    return default_config


def get_llm_vl_config():
    """获取 LLM VL 配置，支持 Nacos 配置未加载的情况"""
    try:
        d = config.get("llm-vl-config")
        if d:
            return d
    except (KeyError, AttributeError):
        pass

    # 如果 Nacos 配置未加载，使用默认配置
    default_config = DEFAULT_CONFIGS["llm-vl-config"]
    print(f"⚠️ 使用默认 llm-vl-config: {default_config}")
    return default_config


def get_redis_config():
    """获取 Redis 配置"""
    try:
        from config.settings import settings

        return {
            "host": settings.REDIS_HOST,
            "port": settings.REDIS_PORT,
            "db": settings.REDIS_DB,
            "password": settings.REDIS_PASSWORD,
        }
    except Exception as e:
        print(f"⚠️ 获取 Redis 配置失败，使用默认配置: {e}")
        return {
            "host": "***********",
            "port": 6379,
            "db": 4,
            "password": "wtg2024@",
        }


def get_vl_config():
    """获取 VL 配置，支持 Nacos 配置未加载的情况（兼容旧函数名）"""
    return get_llm_vl_config()
