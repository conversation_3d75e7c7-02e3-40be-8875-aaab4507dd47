import base64
import json
import logging
import os
import time
import zipfile

import cv2
import fitz
import numpy as np
import patoolib
import requests
from docx import Document
from pypdf import PdfReader, PdfWriter

from configurer.config_reader import get_gate_config
from servers import file_server

logger = logging.getLogger(name=__name__)


def get_url(path):
    config = get_gate_config()
    return config["endpoint"] + "/" + path


def get_header(path):
    config = get_gate_config()
    return {
        "Content-type": "application/json",
        "Authorization": "Bearer " + config["apiKey"],
        "x-fag-appcode": config["appCode"],
        "x-fag-servicename": path,
    }


# 正则表达式模式匹配身份证号
# id_card_pattern1 = r'(^[1-9]\d{5}(18|19|([23]\d))\d{2}((0[1-9])|(10|11|12))(([0-2][1-9])|10|20|30|31)\d{3}[0-9Xx]$)'  # 18位身份证号
# id_card_pattern2 = r'(^[1-9]\d{5}\d{2}((0[1-9])|(10|11|12))(([0-2][1-9])|10|20|30|31)\d{3}$)'  # 15位身份证号
# # 编译正则表达式以提高效率
# id_card_re1 = re.compile(id_card_pattern1)
# id_card_re2 = re.compile(id_card_pattern2)
# # 检查是否匹配
# def valid_id_card(id_card):
#     return bool(id_card_re1.match(id_card) or id_card_re2.match(id_card))
# def valid_phone_number(phone_number):
#     pattern = r'^1\d{10}$'
#     return re.match(pattern, phone_number) is not None


def create_zip(zip_name, files_to_add):
    """
    创建一个包含指定文件的.zip压缩文件。

    :param zip_name: 要创建的.zip文件名。
    :param files_to_add: 一个列表，包含要添加到.zip文件中的文件路径。
    """
    with zipfile.ZipFile(zip_name, "w") as zipf:
        for file in files_to_add:
            # 计算相对于最靠近的公共父目录的相对路径，
            # 假设所有文件具有共同的父目录。如果文件分布于不同目录中，则需要分别处理或给出基准目录。
            arcname = os.path.relpath(file, start=os.path.commonpath(files_to_add))
            zipf.write(file, arcname=arcname)
            logger.info(f"已添加文件 {file} 到 {zip_name} 作为 {arcname}")


def split_by_tag(file_name, pdf_file, path):
    # 打开 PDF 文件
    doc = fitz.open(pdf_file)
    bookmarks = doc.get_toc()
    for i, (level, title, page_num) in enumerate(bookmarks):
        logger.info(f"Level: {level}, Title: {title}, Page: {page_num}")
        # 获取当前书签的页码范围
        start_page = page_num - 1  # PyMuPDF 的页码从 0 开始
        if i < len(bookmarks) - 1:
            end_page = bookmarks[i + 1][2] - 1  # 下一个书签的起始页
        else:
            end_page = len(doc) - 1  # 最后一个书签到文件末尾

        # 创建新的 PDF 文件
        sub_doc = fitz.open()
        sub_doc.insert_pdf(doc, from_page=start_page, to_page=end_page)
        # 保存子文件
        output_path = f"{path}/{file_name + str(i)}.pdf"
        sub_doc.save(output_path)
        logger.info(f"已保存: {output_path}")
        sub_doc.close()
    doc.close()


def unzip_file(zip_path, extract_to="."):
    """
    解压指定的zip文件到指定的目录。

    :param zip_path: 要解压的.zip文件路径。
    :param extract_to: 解压输出的目标目录，默认为当前目录。
    """
    # 确保输出目录存在
    if not os.path.exists(extract_to):
        os.makedirs(extract_to)

    with zipfile.ZipFile(zip_path, "r") as zip_ref:
        # 使用成员信息中的原始文件名，确保支持UTF-8
        for info in zip_ref.infolist():
            try:
                # 尝试将文件名从UTF-8转换（如果已经是UTF-8，则不会改变）
                filename = info.filename.encode("cp437").decode("utf-8")
            except (UnicodeEncodeError, UnicodeDecodeError) as e:
                # 如果转换失败，使用原始文件名
                logger.warning(f"文件名编码转换失败: {info.filename}, 错误: {e}")
                filename = info.filename

            # 修复路径分隔符以适应操作系统
            if os.sep != "/" and os.sep in filename:
                filename = filename.replace(os.sep, "/")

            # 设置新的文件名为UTF-8编码的名称
            info.filename = filename

            # 解压单个文件
            zip_ref.extract(info, extract_to)
            logger.warning(f"已解压: {info.filename}")


def pdf_first_page_to_image(pdf_path, filename, current_path, filename_name, dpi=300):
    """
    将 PDF 第一页转换为图片
    :param pdf_path: PDF 文件路径
    :param output_image_path: 输出图片路径（支持格式：PNG, JPG等）
    :param dpi: 输出图片分辨率
    """
    if not os.path.exists(f"/{current_path}unzip_page_one_dir/{filename_name}"):
        os.makedirs(f"/{current_path}unzip_page_one_dir/{filename_name}")
    # 打开 PDF 文件
    doc = fitz.open(pdf_path)
    try:
        res = {}
        for page in doc:
            # 计算缩放因子（72dpi 是默认值）
            zoom = dpi / 72
            matrix = fitz.Matrix(zoom, zoom)

            # 将页面转换为像素图（Pixmap）
            pix = page.get_pixmap(matrix=matrix)

            # 保存图像
            pix.save(f"/{current_path}unzip_page_one_dir/{filename_name}/{filename}")
            file_key = file_server.upload_zip_local_file(
                f"/{current_path}unzip_page_one_dir/{filename_name}/{filename}", f"unzip-{filename}"
            )
            res = ocr(file_key, filename)
            if res:
                break
    except Exception as e:
        print(f"发生错误：{e}")
        res = None
    finally:
        # 关闭文档
        doc.close()

    return json.dumps(res, ensure_ascii=False, separators=(",", ":"))


def pdf_cut_page_1(path, filename, current_path, filename_name):
    if not os.path.exists(f"/{current_path}unzip_page_one_dir/{filename_name}"):
        os.makedirs(f"/{current_path}unzip_page_one_dir/{filename_name}")

    with open(path, "rb") as file:
        reader = PdfReader(file)
        writer = PdfWriter()
        writer.add_page(reader.pages[0])
        with open(f"/{current_path}unzip_page_one_dir/{filename_name}/{filename}", "wb") as output_file:
            writer.write(output_file)
    # 生成新文件
    return file_server.upload_zip_local_file(
        f"/{current_path}unzip_page_one_dir/{filename_name}/{filename}", f"unzip-{filename}"
    )


def read_first_heading(path, utype):
    if utype == ".doc" or utype == ".docx":
        # 打开文档
        doc = Document(path)

        # 假设第一个段落是标题
        if doc.paragraphs:
            logger.warning(f"文档标题可能是: {doc.paragraphs[0].text}")
            return doc.paragraphs[0].text
        else:
            logger.warning("文档似乎没有内容。")
            return "未匹配"

    else:
        """
            获取PDF文档第一页的第一条文本内容。
            
            :param pdf_path: PDF文件的路径。
            """
        return "未匹配"
        # try:
        # # 打开PDF文件
        # document = fitz.open(path)
        #
        # # 检查是否有页面
        # if len(document) == 0:
        #     print("PDF文档中没有页面。")
        #     return
        #
        # # 获取第一页
        # first_page = document.load_page(2)  # 注意索引从0开始
        #
        # # 提取文本
        # text = first_page.get_text("text")
        #
        # # 将提取的文本按行分割
        # lines = text.splitlines()
        #
        # if lines:
        #     logger.warning(f"PDF第一页的第一条文本内容是: {lines[0]}")
        #     return lines[0].text
        # else:
        # logger.warning("无法在第一页找到任何文本。")

        # except Exception as e:
        #     logger.error(f"处理PDF时发生错误: {e}")


def list_files_in_current_directory(current_directory):
    # 列出当前目录下所有文件和文件夹
    files_and_dirs = os.listdir(current_directory)

    # 过滤出文件
    files = [f for f in files_and_dirs if os.path.isfile(os.path.join(current_directory, f))]
    files_out = []
    for f in files:
        files_out.append(os.path.join(current_directory) + f)
    dir = [f for f in files_and_dirs if os.path.isdir(os.path.join(current_directory, f))]
    for i in dir:
        files_out = list_dir(current_directory + i + "/", files_out)

    logger.warning(f"当前目录下的文件: {files_out}")
    return files_out


def list_dir(current_directory, files_all):
    # 列出当前目录下所有文件和文件夹
    files_and_dirs = os.listdir(current_directory)

    files = [f for f in files_and_dirs if os.path.isfile(os.path.join(current_directory, f))]
    files_out = []
    for f in files:
        files_out.append(os.path.join(current_directory) + f)
    files_all.extend(files_out)
    dir = [f for f in files_and_dirs if os.path.isdir(os.path.join(current_directory, f))]
    for i in dir:
        files_all.extend(list_dir(current_directory + i + "/", files))
    return files_all


def rename_file(old_name, new_name):
    """
    重命名文件或目录。

    :param old_name: 原文件或目录的名称（包括路径）。
    :param new_name: 新的文件或目录名称（包括路径）。
    """
    try:
        # 使用rename方法进行重命名
        os.rename(old_name, new_name)
    except Exception as e:
        logger.warning(f"发生了一个错误：{e}")


def extract_rar_or_other_formats(file_path, extract_to="."):
    """
    解压非.zip格式的压缩文件（例如.rar）到指定目录。

    :param file_path: 要解压的压缩文件路径。
    :param extract_to: 解压输出的目标目录，默认为当前目录。
    """
    try:
        patoolib.extract_archive(file_path, outdir=extract_to)
    except Exception as e:
        logger.warning(f"发生了一个错误：{e}")
    logger.warning(f"已解压文件至: {extract_to}")


def mosaic_polygon(image, coords, mosaic_size=3, border_thickness=2, option="0"):
    # 创建一个掩膜，大小与原图相同，初始值为0（黑色）

    for coord in coords:
        mask = np.zeros(image.shape[:2], dtype=np.uint8)

        # 将四边形区域填充为白色，表示要进行马赛克处理的区域
        cv2.fillConvexPoly(mask, np.array(coord), color=255)

        # 获取掩膜中的非零坐标，即四边形内的所有像素点
        points = np.column_stack(np.where(mask > 0))

        gray_color = [128, 128, 128]  # 固定的灰色，BGR格式

        for y, x in points:
            # 对每个像素计算其所属的小方块
            tile_x_start = (x // mosaic_size) * mosaic_size
            tile_y_start = (y // mosaic_size) * mosaic_size

            # 定义当前块的边界（确保不超过图像尺寸）
            tile_x_end = min(tile_x_start + mosaic_size, image.shape[1])
            tile_y_end = min(tile_y_start + mosaic_size, image.shape[0])

            # 应用固定的灰色到当前块
            image[tile_y_start:tile_y_end, tile_x_start:tile_x_end] = gray_color

        color = [0, 0, 255]
        # 绘制红框
        if "1" == option:
            color = [255, 0, 0]
        logger.warning(f"mosaic_polygon color:{color}")
        cv2.polylines(image, [np.array(coord)], isClosed=True, color=color, thickness=border_thickness)

    return image


def url_to_base64(url):
    # 下载资源
    response = requests.get(url)
    response.raise_for_status()  # 检查是否下载成功

    # 将内容转换为Base64
    base64_data = base64.b64encode(response.content).decode("utf-8")
    return base64_data


def base64_to_np_image(base64_str):
    # 解码Base64字符串为字节对象
    image_data = base64.b64decode(base64_str)

    # 将字节对象转换为NumPy数组
    image_array = np.frombuffer(image_data, dtype=np.uint8)

    # 使用OpenCV解码图像数据
    np_image = cv2.imdecode(image_array, cv2.IMREAD_COLOR)

    return np_image


def rotate_pic(file_key, file_name):
    response = requests.post(
        get_url("rotate"), headers=get_header("rotate"), json={"file_name": file_name, "file_key": file_key}
    )
    logger.info(f"rotate_pic post info :{response.text}")
    if response.status_code != 200:
        logger.error(f"rotate_pic post error :{response.text}")
        return None
    res = json.loads(response.text)
    return res["result"]


def uc_pic(file_key, file_name):
    response = requests.post(
        get_url("udoc"), headers=get_header("udoc"), json={"file_name": file_name, "file_key": file_key}
    )
    logger.info(f"udoc_pic post info :{response.text}")
    if response.status_code != 200:
        logger.error(f"udoc_pic post error :{response.text}")
        return None
    res = json.loads(response.text)
    return res["result"]


def ocr(file_key, file_name):
    response = requests.post(
        get_url("ocr"), headers=get_header("ocr"), json={"file_name": file_name, "file_key": file_key}
    )
    if response.status_code != 200:
        logger.error(f"ocr post error :{response.text}")
        return None
    res = json.loads(response.text)
    return res["result"]


def analysis(file_key, source_name, cut_point=[], type="0"):
    logger.warning(f"file_key:{file_key}")
    logger.warning(f"source_name:{source_name}")

    source_url = file_server.get_file_url(file_key)
    base64 = url_to_base64(source_url)
    np_image = base64_to_np_image(base64)

    flag = False
    if not cut_point:
        start_time = time.time()  # 记录结束时间
        # 发送HTTP请求
        response = requests.post(
            get_url("ocr"), headers=get_header("ocr"), json={"file_name": source_name, "file_key": file_key}
        )
        if response.status_code != 200:
            logger.error(f"ocr post error :{response.text}")
            return None
        res = json.loads(response.text)
        data = res["result"]
        # results = ocr.recognize_text(
        #     images=[np_image],  # 图片数据，ndarray.shape 为 [H, W, C]，BGR格式；
        #     use_gpu=True,  # 是否使用 GPU；若使用GPU，请先设置CUDA_VISIBLE_DEVICES环境变量
        #     output_dir='ocr_result',  # 图片的保存路径，默认设为 ocr_result；
        #     visualization=True,  # 是否将识别结果保存为图片文件；
        #     box_thresh=0.5,  # 检测文本框置信度的阈值；
        #     text_thresh=0.5)  # 识别中文文本置信度的阈值；
        # data = results[0]['data']

        # data = {'images': [base64],
        #         'use_gpu': True,
        #         "box_thresh": 0.5,
        #         "text_thresh": 0.7}
        # r = requests.post(url=url, headers=headers, data=json.dumps(data))
        end_time = time.time()  # 记录结束时间
        logger.warning(f"ocr process took {end_time - start_time:.2f} seconds")

        start_time = time.time()  # 记录结束时间
        nerResponse = requests.post(get_url("ner"), headers=get_header("ner"), json={"data": data})
        logger.warning(f"nerResponse:{nerResponse.text}")
        if nerResponse.status_code != 200:
            logger.error(f"ner post error :{response.text}")
            return None
        text_position_list = json.loads(nerResponse.text)["result"]
        logger.warning(f"text_position_list:{text_position_list}")
        for text_position in text_position_list:
            flag = True
            mosaic_polygon(np_image, text_position)
        # ocr接口
        response = requests.post(
            get_url("ocr"), headers=get_header("ocr"), json={"file_name": source_name, "file_key": file_key}
        )
        if response.status_code != 200:
            logger.error(f"ocr post error :{response.text}")
            return None
        res = json.loads(response.text)
        data = res["result"]

        end_time = time.time()  # 记录结束时间
        logger.warning(f"ocr process took {end_time - start_time:.2f} seconds")
        # ner接口
        start_time = time.time()  # 记录结束时间
        nerResponse = requests.post(get_url("ner"), headers=get_header("ner"), json={"data": data})
        logger.warning(f"nerResponse:{nerResponse.text}")
        if nerResponse.status_code != 200:
            logger.error(f"ner post error :{response.text}")
            return None
        text_position_list = json.loads(nerResponse.text)["result"]
        logger.warning(f"text_position_list:{text_position_list}")
        for text_position in text_position_list:
            flag = True
            mosaic_polygon(np_image, text_position, 3, 2, type)

        end_time = time.time()  # 记录结束时间
        logger.warning(f"ner process took {end_time - start_time:.2f} seconds")

    else:
        flag = True
        start_time = time.time()  # 记录结束时间
        mosaic_polygon(np_image, cut_point, 3, 2, type)
        end_time = time.time()  # 记录结束时间
        logger.warning(f"do mosaic {type} took {end_time - start_time:.2f} seconds")

    if flag:
        return np_image
    else:
        return None


if __name__ == "__main__":
    # file_name="aaa.png"
    # start_time = time.time()  # 记录结束时间
    #
    # # 下载资源
    # response = requests.get('https://yiya-dev.oss-cn-hangzhou.aliyuncs.com/oss-20241203130207855aWwFVBBkfXdF6bf.png?OSSAccessKeyId=LTAI5tSMtzYQ5GVPCm8njDYp&Expires=1736493909&Signature=RxFEcrM2h88WaKVEiAzD7EDoRjg%3D')
    # response.raise_for_status()  # 检查是否下载成功
    #
    # # 将内容转换为Base64
    # base64_data = base64.b64encode(response.content).decode('utf-8')
    # # 解码Base64字符串为字节对象
    # image_data = base64.b64decode(base64_data)
    #
    # # 将字节对象转换为NumPy数组
    # image_array = np.frombuffer(image_data, dtype=np.uint8)
    #
    # # 使用OpenCV解码图像数据
    # np_image = cv2.imdecode(image_array, cv2.IMREAD_COLOR)
    #
    # file_base_name, file_extension = os.path.splitext(os.path.basename(file_name))
    # # 构造新的文件名
    # file_name = f"{file_base_name}_{file_extension}"
    #
    # start_time = time.time()
    # file_key = file_server.put_object(np_image,file_name)
    # print(file_key)
    file_url = file_server.get_file_url("oss/tmp/20241231-173319-0_20250103080737.jpeg")
    print(file_url)

    # file_name="aaa.png"
    # start_time = time.time()  # 记录结束时间
    #
    # # 下载资源
    # response = requests.get('https://yiya-dev.oss-cn-hangzhou.aliyuncs.com/oss-20241203130207855aWwFVBBkfXdF6bf.png?OSSAccessKeyId=LTAI5tSMtzYQ5GVPCm8njDYp&Expires=1736493909&Signature=RxFEcrM2h88WaKVEiAzD7EDoRjg%3D')
    # response.raise_for_status()  # 检查是否下载成功
    #
    # # 将内容转换为Base64
    # base64_data = base64.b64encode(response.content).decode('utf-8')
    # # 解码Base64字符串为字节对象
    # image_data = base64.b64decode(base64_data)
    #
    # # 将字节对象转换为NumPy数组
    # image_array = np.frombuffer(image_data, dtype=np.uint8)
    #
    # # 使用OpenCV解码图像数据
    # np_image = cv2.imdecode(image_array, cv2.IMREAD_COLOR)
    #
    # file_base_name, file_extension = os.path.splitext(os.path.basename(file_name))
    # # 构造新的文件名
    # file_name = f"{file_base_name}_{file_extension}"
    #
    # start_time = time.time()
    # file_key = file_server.put_object(np_image,file_name)
    # print(file_key)
    file_url = file_server.get_file_url("oss/tmp/20241231-173319-0_20250103080737.jpeg")
    print(file_url)
