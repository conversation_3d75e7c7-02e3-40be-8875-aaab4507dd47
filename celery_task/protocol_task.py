import io
import os
import time
import zipfile
import tempfile
import subprocess
import shutil
from datetime import datetime
from typing import Any, Dict

from celery import Task
from oss2.exceptions import NoSuch<PERSON>ey

from celery_task.celery import celery_app
from logger.logger import app_logger
from utils.concurrency_manager import file_lock, task_limit
from utils.memory_manager import memory_limit, memory_manager
from utils.monitoring_system import record_metric
from utils.oss_utils import download_file_to_stream, upload_key_information_to_oss, upload_sections_to_oss
from utils.protocol_utils import extract_structured_protocol


class UnsupportedFileTypeError(Exception):
    """用户上传的文件不是有效的 DOCX，或是 DOC 被改了后缀。"""
    pass


def sniff_office_type(data: bytes) -> str:
    """
    简单嗅探：返回 'docx' | 'doc' | 'unknown'
    - docx: zip 容器
    - doc : OLE/CFB (D0 CF 11 E0 A1 B1 1A E1)
    """
    bio = io.BytesIO(data)
    try:
        if zipfile.is_zipfile(bio):
            return "docx"
    finally:
        try:
            bio.seek(0)
        except Exception:
            pass

    if len(data) >= 8 and data[:8] == b"\xD0\xCF\x11\xE0\xA1\xB1\x1A\xE1":
        return "doc"
    return "unknown"


def try_convert_doc_to_docx(doc_bytes: bytes, timeout: int = 90) -> bytes | None:
    """
    依赖 LibreOffice(soffice)；通过环境变量 ENABLE_DOC_CONVERSION=true 启用；
    成功返回 docx 字节，失败返回 None。
    """
    if os.environ.get("ENABLE_DOC_CONVERSION", "false").lower() != "true":
        return None

    tmpdir = tempfile.mkdtemp(prefix="doc2docx_")
    in_path = os.path.join(tmpdir, "input.doc")
    out_path = os.path.join(tmpdir, "input.docx")
    try:
        with open(in_path, "wb") as f:
            f.write(doc_bytes)

        cmd = [
            "soffice", "--headless", "--norestore", "--nodefault", "--nolockcheck",
            "--convert-to", "docx", "--outdir", tmpdir, in_path
        ]
        proc = subprocess.run(cmd, stdout=subprocess.PIPE, stderr=subprocess.PIPE, timeout=timeout)
        if proc.returncode != 0 or not os.path.exists(out_path):
            app_logger.error(
                f"LibreOffice 转换失败 rc={proc.returncode}, "
                f"stdout={proc.stdout.decode('utf-8', 'ignore')}, "
                f"stderr={proc.stderr.decode('utf-8', 'ignore')}"
            )
            return None

        with open(out_path, "rb") as f:
            return f.read()
    except Exception as e:
        app_logger.error(f"DOC->DOCX 转换过程异常: {e}")
        return None
    finally:
        shutil.rmtree(tmpdir, ignore_errors=True)


def safe_update_state(task_instance, state, meta, max_retries=3):
    """
    安全的状态更新函数，带重试机制和分布式锁
    """
    from utils.distributed_lock import TaskStateLock

    task_id = task_instance.request.id

    try:
        # 使用分布式锁防止并发状态更新
        with TaskStateLock.lock_task_state(task_id, timeout=10):
            for attempt in range(max_retries):
                try:
                    # 先检查当前状态，避免状态回退
                    from celery_task.celery import celery_app

                    current_result = celery_app.AsyncResult(task_id)
                    current_state = current_result.state

                    # 定义状态优先级（数字越大优先级越高）
                    state_priority = {
                        "PENDING": 0,
                        "RETRY": 1,
                        "PROGRESS": 2,
                        "SUCCESS": 3,
                        "FAILURE": 3,
                        "REVOKED": 3,
                        "RECOVERED": 2,
                    }

                    current_priority = state_priority.get(current_state, 0)
                    new_priority = state_priority.get(state, 0)

                    # 只允许状态向前推进或同级更新
                    if new_priority >= current_priority:
                        task_instance.update_state(state=state, meta=meta)
                        app_logger.debug(f"任务状态更新成功: {task_id} {current_state} -> {state}")
                        return True
                    else:
                        app_logger.warning(f"拒绝状态回退: {task_id} {current_state} -> {state}")
                        return False

                except Exception as e:
                    app_logger.warning(f"状态更新失败 (尝试 {attempt + 1}/{max_retries}): {e}")
                    app_logger.warning(f"任务ID: {task_id}, 目标状态: {state}, 错误类型: {type(e).__name__}")
                    if attempt == max_retries - 1:
                        app_logger.error(f"状态更新最终失败，跳过此次更新。任务ID: {task_id}")
                        # 尝试降级为简单状态更新
                        try:
                            task_instance.update_state(state=state, meta=meta)
                            app_logger.info(f"降级状态更新成功: {task_id}")
                            return True
                        except Exception as fallback_e:
                            app_logger.error(f"降级状态更新也失败: {fallback_e}")
                            return False
                    time.sleep(1)  # 等待1秒后重试
            return False

    except Exception as e:
        app_logger.error(f"获取状态更新锁失败: {e}")
        # 如果无法获取锁，降级为普通更新
        try:
            task_instance.update_state(state=state, meta=meta)
            return True
        except Exception as fallback_e:
            app_logger.error(f"降级状态更新也失败: {fallback_e}")
            return False


@celery_app.task(
    name="extract_protocol_task",
    bind=True,
    autoretry_for=(ConnectionError, TimeoutError, MemoryError, OSError),
    retry_kwargs={"max_retries": 3, "countdown": 120},
    retry_backoff=True,
    retry_backoff_max=600,
    retry_jitter=True,
    # 问题2修复：添加任务超时时间（5分钟）
    time_limit=300,  # 硬超时：5分钟
    soft_time_limit=240,  # 软超时：4分钟，给任务清理时间
)
def extract_protocol_task(self: Task, file_key: str, extract_keys_only: bool = False) -> Dict[str, Any]:
    """
    协议文档解析任务：
    1. 从 OSS 下载 .docx 文件
    2. 解析出层级化 JSON 结构
    3. 将 sections 部分上传回 OSS
    4. 返回 key_information 和 OSS 路径
    """
    task_id = self.request.id
    app_logger.info(f"开始执行协议解析任务，Task ID: {task_id}, File Key: {file_key}")

    global_start_time = time.time()
    stage_times = {}

    # 启动内存监控和设置内存限制
    memory_manager.start_monitoring(interval=10)  # 10秒检查一次

    # 根据文件大小动态设置内存限制
    initial_memory = memory_manager.get_memory_usage()
    app_logger.info(f"任务开始时内存使用: {initial_memory['rss_mb']:.2f} MB")

    # 添加内存告警回调
    def memory_warning_callback(data):
        app_logger.warning(f"任务 {task_id} 内存使用警告: {data['usage']['rss_mb']:.2f} MB")

    def memory_critical_callback(data):
        app_logger.error(f"任务 {task_id} 内存使用临界: {data['usage']['rss_mb']:.2f} MB，强制垃圾回收")
        memory_manager.force_gc(rounds=3)

    memory_manager.add_callback("warning", memory_warning_callback)
    memory_manager.add_callback("critical", memory_critical_callback)

    # 使用并发控制，限流检查已移至API层面，避免重复检查导致冲突
    with task_limit("protocol_extraction", max_concurrent=3):  # 最多3个并发解析任务
        return _execute_protocol_task(
            self, task_id, file_key, extract_keys_only, global_start_time, stage_times, initial_memory
        )


def _check_task_timeout(task_id: str, start_time: float, timeout_seconds: int = 240) -> None:
    """
    检查任务是否超时，如果超时则抛出异常

    Args:
        task_id: 任务ID
        start_time: 任务开始时间
        timeout_seconds: 超时时间（秒），默认4分钟

    Raises:
        Exception: 如果任务超时
    """
    elapsed_time = time.time() - start_time
    if elapsed_time > timeout_seconds:
        error_msg = f"任务 {task_id} 执行超时，已运行 {elapsed_time:.1f} 秒，超过限制 {timeout_seconds} 秒"
        app_logger.error(error_msg)
        raise Exception(error_msg)


def _execute_protocol_task(
    task_instance, task_id, file_key, extract_keys_only, global_start_time, stage_times, initial_memory
):
    # 初始化任务状态
    task_info = {
        "task_id": task_id,
        "file_key": file_key,
        "extract_keys_only": extract_keys_only,
        "current_stage": "初始化",
        "progress": 0,
        "created_at": datetime.now().strftime("%Y-%m-%d %H:%M:%S"),
        "updated_at": datetime.now().strftime("%Y-%m-%d %H:%M:%S"),
    }

    try:
        # 问题2修复：在每个阶段开始前检查超时
        _check_task_timeout(task_id, global_start_time)

        # 阶段1: 下载文件
        stage_start = time.time()
        task_info.update(
            {
                "current_stage": "下载 DOCX 文件",
                "progress": 10,
                "updated_at": datetime.now().strftime("%Y-%m-%d %H:%M:%S"),
            }
        )
        safe_update_state(task_instance, "PROGRESS", task_info)

        app_logger.info(f"正在从OSS下载文件: {file_key}")
        # 使用文件锁防止并发下载同一文件
        with file_lock(file_key, mode="read"):
            oss_stream = download_file_to_stream(file_key)
        download_time = time.time() - stage_start
        stage_times["download"] = download_time
        app_logger.info(f"文件下载完成，耗时: {download_time:.2f}s")

        # 记录下载性能指标
        record_metric("task_download_time", download_time, {"file_key": file_key})

        # 下载完成后再次检查超时
        _check_task_timeout(task_id, global_start_time)

        # 阶段2: 处理文件（解析docx）
        stage_start = time.time()
        task_info.update(
            {
                "current_stage": "解析 DOCX 文档",
                "progress": 30,
                "updated_at": datetime.now().strftime("%Y-%m-%d %H:%M:%S"),
            }
        )
        safe_update_state(task_instance, "PROGRESS", task_info)

        # 优化内存使用：分步处理大文件
        try:
            oss_data = oss_stream.read()
        finally:
            # 确保流被正确关闭
            if hasattr(oss_stream, "close"):
                oss_stream.close()

        # ---- 文件类型嗅探/转换（核心插入点）----
        file_type = sniff_office_type(oss_data)

        if file_type == "doc":
            app_logger.warning("检测到用户上传的是 DOC (OLE/CFB) 文件，但后缀为 .docx；尝试自动转换")
            converted = try_convert_doc_to_docx(oss_data)
            if converted:
                oss_data = converted
                file_type = "docx"
                app_logger.info("DOC->DOCX 自动转换成功，继续后续解析")
            else:
                # 友好失败：可序列化、不会触发“Exception information must include the exception type”
                raise UnsupportedFileTypeError(
                    "上传的文件不是有效的 DOCX（检测为旧版 DOC）。请上传原始 DOCX，或启用自动转换。"
                )
        elif file_type != "docx":
            raise UnsupportedFileTypeError(
                "上传的文件不是有效的 DOCX（非 zip 容器）。请上传正确的 .docx 文件。"
            )
        # ---- 嗅探/转换结束 ----

        # 检查文件大小，对大文件进行特殊处理（转换后尺寸才准确）
        file_size_mb = len(oss_data) / (1024 * 1024)
        app_logger.info(f"文件大小: {file_size_mb:.2f}MB")

        # 根据文件大小动态设置内存限制
        if file_size_mb > 50:  # 大于50MB的文件
            memory_limit_mb = int(file_size_mb * 8)  # 8倍文件大小
        elif file_size_mb > 10:  # 大于10MB的文件
            memory_limit_mb = int(file_size_mb * 6)  # 6倍文件大小
        else:
            memory_limit_mb = 1024  # 1GB默认限制

        app_logger.info(f"设置内存限制: {memory_limit_mb} MB")

        # 文档处理前检查超时
        _check_task_timeout(task_id, global_start_time)

        # 使用内存限制上下文处理文件
        with memory_limit(memory_limit_mb, auto_gc=True):
            if file_size_mb > 10:  # 大于10MB的文件
                app_logger.warning(f"处理大文件 ({file_size_mb:.2f}MB)，启用内存优化模式")
                # 强制垃圾回收
                memory_manager.force_gc()

            with io.BytesIO(oss_data) as docx_stream:
                # 清理原始数据引用
                del oss_data
                memory_manager.force_gc()

                # 这里如果用户强改后缀绕过了嗅探（极少数边界），依然可能抛 BadZipFile
                try:
                    protocol_data = extract_structured_protocol(docx_stream, extract_keys_only=extract_keys_only)
                except zipfile.BadZipFile as z:
                    raise UnsupportedFileTypeError(
                        "DOCX 容器损坏或并非有效的 DOCX 文件。请上传正确的 .docx 文件。"
                    ) from z

        process_time = time.time() - stage_start
        stage_times["process"] = process_time
        app_logger.info(f"文档解析完成，耗时: {process_time:.2f}s")

        # 记录解析性能指标
        record_metric("task_process_time", process_time, {"file_size_mb": str(file_size_mb)})
        record_metric("file_size_processed", file_size_mb)

        # 将解析结果分离：key_information、sections 和 shared_ooxml_parts 分别上传到OSS
        key_information = protocol_data.get("key_information", {})
        sections_data = protocol_data.get("sections", [])
        shared_ooxml_parts = protocol_data.get("shared_ooxml_parts", {})

        # 上传前检查超时
        _check_task_timeout(task_id, global_start_time)

        # 阶段3: 上传数据到OSS
        stage_start = time.time()
        task_info.update(
            {
                "current_stage": "上传数据到 OSS",
                "progress": 60,
                "updated_at": datetime.now().strftime("%Y-%m-%d %H:%M:%S"),
            }
        )
        safe_update_state(task_instance, "PROGRESS", task_info)

        # 分别上传sections和key_information到OSS，同时包含shared_ooxml_parts
        sections_oss_key = None
        key_information_oss_key = None

        try:
            # 上传sections数据（包含shared_ooxml_parts）
            if sections_data:
                app_logger.info(f"开始上传sections数据，包含 {len(sections_data)} 个章节")
                sections_oss_key = upload_sections_to_oss(sections_data, shared_ooxml_parts)
                if sections_oss_key:
                    app_logger.info(f"sections数据上传成功: {sections_oss_key}")
                else:
                    app_logger.warning("sections数据上传失败，OSS可能未配置")

            # 上传key_information数据（包含shared_ooxml_parts）
            if key_information:
                app_logger.info(f"开始上传key_information数据，包含字段: {list(key_information.keys())}")
                key_information_oss_key = upload_key_information_to_oss(key_information, shared_ooxml_parts)
                if key_information_oss_key:
                    app_logger.info(f"key_information数据上传成功: {key_information_oss_key}")
                else:
                    app_logger.warning("key_information数据上传失败，OSS可能未配置")

        except Exception as e:
            app_logger.error(f"上传数据到OSS时发生错误: {e}")
            # 继续执行，不中断任务

        upload_time = time.time() - stage_start
        stage_times["upload"] = upload_time
        app_logger.info(f"OSS上传阶段完成，耗时: {upload_time:.2f}s")

        # 强制内存清理
        del protocol_data
        # 注意：不要立即 del sections_data；稍后构造 final_result 时可能需要返回它

        # 使用内存管理器进行清理
        gc_result = memory_manager.force_gc(rounds=3)
        app_logger.info(
            f"内存清理结果: 回收 {gc_result['collected_objects']} 个对象, 释放 {gc_result['freed_mb']:.2f} MB"
        )

        # 记录内存使用情况
        final_memory = memory_manager.get_memory_usage()
        app_logger.info(f"任务完成后内存使用: {final_memory['rss_mb']:.2f} MB")

        # 计算内存使用效率
        memory_growth = final_memory["rss_mb"] - initial_memory["rss_mb"]
        app_logger.info(f"任务内存增长: {memory_growth:.2f} MB")

        # 计算总耗时和性能统计
        total_time = time.time() - global_start_time
        stage_times["total"] = total_time

        # 性能报告（保留2位小数）
        performance_report = {
            "stage_times": {k: round(v, 2) for k, v in stage_times.items()},
            "file_size_mb": round(file_size_mb, 2),
            "throughput": {
                "download": round(file_size_mb / download_time, 2)
                if "download" in stage_times and download_time > 0
                else 0,
                "upload": round(file_size_mb / upload_time, 2) if "upload" in stage_times and upload_time > 0 else 0,
            },
        }

        app_logger.info(
            f"性能统计 - 总耗时: {round(total_time, 2)}s, "
            f"下载: {round(stage_times.get('download', 0), 2)}s, "
            f"处理: {round(stage_times.get('process', 0), 2)}s, "
            f"上传: {round(stage_times.get('upload', 0), 2)}s"
        )

        # 记录任务成功指标
        record_metric("task_success", 1, {"task_type": "protocol_extraction"})
        record_metric("task_total_time", total_time)
        record_metric("task_memory_growth", memory_growth)
        record_metric("task_throughput", file_size_mb / total_time if total_time > 0 else 0)

        # 最终结果
        final_result = {
            "task_id": task_id,
            "status": "success",
            "progress": 100,
            "current_stage": "完成",
            "file_key": file_key,
            "extract_keys_only": extract_keys_only,
            "performance_report": performance_report,
            "total_time_seconds": round(total_time, 2),
            "created_at": task_info["created_at"],
            "updated_at": datetime.now().strftime("%Y-%m-%d %H:%M:%S"),
            "completed_at": datetime.now().strftime("%Y-%m-%d %H:%M:%S"),
        }

        # 添加OSS键（如果上传成功）
        if sections_oss_key:
            final_result["sections_oss_key"] = sections_oss_key
            app_logger.info("sections已上传到OSS，只返回OSS key")
        else:
            # OSS未配置或上传失败，直接返回数据
            final_result["sections"] = sections_data

        if key_information_oss_key:
            final_result["key_information_oss_key"] = key_information_oss_key
            app_logger.info("key_information已上传到OSS，只返回OSS key")
        else:
            # OSS未配置或上传失败，直接返回数据
            final_result["key_information"] = key_information

        # 现在可以安全释放 sections_data
        try:
            del sections_data
        except Exception:
            pass

        app_logger.info(f"协议解析任务完成，Task ID: {task_id}, 耗时: {total_time:.2f}秒")
        return final_result

    except NoSuchKey:
        error_msg = f"OSS文件未找到 (NoSuchKey)，请求的Key为: '{file_key}'"
        app_logger.error(error_msg)

        # 更新任务状态为失败
        error_info = {
            "task_id": task_id,
            "file_key": file_key,
            "extract_keys_only": extract_keys_only,
            "current_stage": "文件下载失败",
            "progress": 0,
            "error": error_msg,
            "created_at": task_info.get("created_at", datetime.now().strftime("%Y-%m-%d %H:%M:%S")),
            "updated_at": datetime.now().strftime("%Y-%m-%d %H:%M:%S"),
        }
        safe_update_state(task_instance, "FAILURE", error_info)

        # 记录任务失败指标
        record_metric(
            "task_failure",
            1,
            {
                "task_type": "protocol_extraction",
                "error_type": "NoSuchKey",
                "file_not_found": "true",
            },
        )

        app_logger.error(f"即将抛出 NoSuchKey 异常: {error_msg}")
        raise Exception(error_msg)

    except Exception as e:
        app_logger.error(f"协议解析任务失败，Task ID: {task_id}, 错误: {str(e)}", exc_info=True)

        error_str = str(e).lower()
        is_memory_error = any(
            keyword in error_str
            for keyword in [
                "memory",
                "oom",
                "killed",
                "out of memory",
                "cannot allocate",
                "resource temporarily unavailable",
            ]
        )
        is_connection_error = any(
            keyword in error_str for keyword in ["connection", "timeout", "network", "redis", "broker"]
        )
        is_user_file_error = isinstance(e, UnsupportedFileTypeError) or "badzipfile" in error_str

        # 如果是内存或连接错误，且还有重试次数，则重试；用户文件错误不重试
        max_retries = getattr(task_instance, "max_retries", 3)
        current_retries = task_instance.request.retries

        if (is_memory_error or is_connection_error) and not is_user_file_error and current_retries < max_retries:
            app_logger.warning(f"检测到可重试错误 (重试 {current_retries + 1}/{max_retries}): {e}")

            # 计算重试延迟（指数退避）
            countdown = min(120 * (2**current_retries), 600)  # 最大10分钟

            # 更新任务状态为重试中
            retry_info = {
                "task_id": task_id,
                "file_key": file_key,
                "extract_keys_only": extract_keys_only,
                "current_stage": f"任务重试中 ({current_retries + 1}/{max_retries})",
                "progress": 5,
                "error": f"检测到可重试错误，{countdown}秒后重试: {str(e)}",
                "created_at": task_info.get("created_at", datetime.now().strftime("%Y-%m-%d %H:%M:%S")),
                "updated_at": datetime.now().strftime("%Y-%m-%d %H:%M:%S"),
            }
            safe_update_state(task_instance, "RETRY", retry_info)

            # 强制垃圾回收，释放内存
            memory_manager.force_gc()

            # 抛出重试异常
            raise task_instance.retry(countdown=countdown, exc=e)

        # 更新任务状态为失败（不可重试或已达上限或用户文件错误）
        error_info = {
            "task_id": task_id,
            "file_key": file_key,
            "extract_keys_only": extract_keys_only,
            "current_stage": "任务失败",
            "progress": 0,
            "error": str(e),
            "created_at": task_info.get("created_at", datetime.now().strftime("%Y-%m-%d %H:%M:%S")),
            "updated_at": datetime.now().strftime("%Y-%m-%d %H:%M:%S"),
        }
        safe_update_state(task_instance, "FAILURE", error_info)

        # 记录任务失败指标
        record_metric(
            "task_failure",
            1,
            {
                "task_type": "protocol_extraction",
                "error_type": type(e).__name__,
                "is_memory_error": str(is_memory_error),
                "is_connection_error": str(is_connection_error),
                "is_user_file_error": str(is_user_file_error),
            },
        )

        error_msg = f"协议解析任务执行失败: {str(e)}"
        raise Exception(error_msg)