#!/usr/bin/env python3.11
"""
Celery 任务恢复模块
在 Worker 启动时自动检测和恢复中断的任务
"""

import json
from datetime import datetime, timedelta
from typing import Any, Dict, List

from logger.logger import app_logger
from utils.redis_pool import get_redis_client


class TaskRecovery:
    """任务恢复管理器"""

    def __init__(self, redis_host: str = None, redis_port: int = None, redis_db: int = None):
        """初始化任务恢复管理器"""
        # 从 Celery 配置中获取 Redis 连接信息
        try:
            from configurer.config_reader import get_redis_config

            # 尝试从配置中获取 Redis 信息
            redis_config = get_redis_config()

            host = redis_config.get("host", "***********")
            port = redis_config.get("port", 6379)
            db = redis_config.get("db", 4)
            password = redis_config.get("password", "")

        except Exception as e:
            app_logger.warning(f"无法从配置获取 Redis 信息，使用默认配置: {e}")
            # 使用默认配置
            host = redis_host or "***********"
            port = redis_port or 6379
            db = redis_db or 4
            password = ""

        # 使用连接池管理器获取 Redis 客户端
        self.redis_client = get_redis_client(
            pool_name="task_recovery",
            host=host,
            port=port,
            db=db,
            password=password,
            max_connections=10,  # 任务恢复不需要太多连接
            socket_connect_timeout=10,
            socket_timeout=30,
            retry_on_timeout=True,
            health_check_interval=30,
        )

    def find_interrupted_tasks(self, max_age_minutes: int = 10) -> List[Dict[str, Any]]:
        """
        查找被中断的任务

        Args:
            max_age_minutes: 最大任务年龄（分钟），超过此时间的任务被认为是僵尸任务

        Returns:
            被中断任务的列表
        """
        interrupted_tasks = []
        cutoff_time = datetime.now() - timedelta(minutes=max_age_minutes)

        try:
            # 获取所有任务键
            task_keys = self.redis_client.keys("celery-task-meta-*")

            # 预过滤：只检查可能是 PROGRESS 状态的任务
            progress_tasks = []
            total_tasks = len(task_keys)

            for key in task_keys:
                try:
                    result = self.redis_client.get(key)
                    if not result:
                        continue

                    data = json.loads(result)
                    status = data.get("status", "")

                    # 只处理 PROGRESS 状态的任务，但排除测试任务
                    if status == "PROGRESS":
                        # 检查是否是测试任务
                        result = data.get("result", {})
                        file_key = result.get("file_key", "")

                        # 过滤掉测试任务
                        if any(
                            test_pattern in file_key
                            for test_pattern in [
                                "protocol/test/",
                                "timeout-test-file",
                                "non-existent-file",
                                "/test/",
                                "test-file",
                            ]
                        ):
                            app_logger.debug(f"跳过测试任务: {file_key}")
                            continue

                        progress_tasks.append((key, data))

                except (json.JSONDecodeError, KeyError):
                    continue

            app_logger.info(f"检查 {len(progress_tasks)} 个 PROGRESS 任务 (总任务数: {total_tasks})")

            for key, data in progress_tasks:
                try:
                    task_id = key.replace("celery-task-meta-", "")
                    result_data = data.get("result", {})

                    # 检查任务更新时间
                    updated_at_str = result_data.get("updated_at", "")
                    if not updated_at_str:
                        continue

                    try:
                        updated_at = datetime.strptime(updated_at_str, "%Y-%m-%d %H:%M:%S")

                        # 如果任务超过指定时间没有更新，认为是被中断的
                        if updated_at < cutoff_time:
                            task_info = {
                                "task_id": task_id,
                                "file_key": result_data.get("file_key", ""),
                                "extract_keys_only": result_data.get("extract_keys_only", False),
                                "current_stage": result_data.get("current_stage", ""),
                                "progress": result_data.get("progress", 0),
                                "updated_at": updated_at_str,
                                "age_hours": (datetime.now() - updated_at).total_seconds() / 3600,
                            }
                            interrupted_tasks.append(task_info)

                    except ValueError:
                        app_logger.warning(f"无法解析任务 {task_id} 的更新时间: {updated_at_str}")
                        continue

                except (json.JSONDecodeError, KeyError) as e:
                    app_logger.warning(f"解析任务数据失败 {key}: {e}")
                    continue

        except Exception as e:
            app_logger.error(f"查找中断任务时发生错误: {e}")

        return interrupted_tasks

    def recover_task(self, task_info: Dict[str, Any]) -> bool:
        """
        恢复单个任务

        Args:
            task_info: 任务信息字典

        Returns:
            是否成功恢复
        """
        task_id = task_info["task_id"]
        file_key = task_info["file_key"]
        extract_keys_only = task_info["extract_keys_only"]

        try:
            app_logger.info(f"恢复任务 {task_id}: {file_key}")

            # 导入 Celery 应用和任务
            from celery_task.celery import celery_app
            from celery_task.protocol_task import extract_protocol_task

            # 清理原任务状态
            original_result = celery_app.AsyncResult(task_id)
            original_result.forget()

            # 重新提交任务（使用新的任务ID）
            new_task = extract_protocol_task.delay(file_key, extract_keys_only)

            app_logger.info(f"任务已重新提交: {task_id} -> {new_task.id}")

            # 更新原任务状态，指向新任务
            recovery_info = {
                "task_id": task_id,
                "status": "recovered",
                "new_task_id": new_task.id,
                "file_key": file_key,
                "extract_keys_only": extract_keys_only,
                "current_stage": "任务已恢复",
                "progress": 0,
                "recovered_at": datetime.now().strftime("%Y-%m-%d %H:%M:%S"),
                "original_stage": task_info.get("current_stage", ""),
                "message": f"原任务已恢复为新任务 {new_task.id}",
            }

            # 保存恢复信息
            recovery_key = f"celery-task-meta-{task_id}"
            self.redis_client.set(
                recovery_key,
                json.dumps({"status": "RECOVERED", "result": recovery_info}),
                ex=86400,  # 24小时过期
            )

            return True

        except Exception as e:
            app_logger.error(f"恢复任务 {task_id} 失败: {e}")
            return False

    def recover_all_interrupted_tasks(self, max_age_minutes: int = 10, max_recovery_count: int = 10) -> Dict[str, Any]:
        """
        恢复所有被中断的任务

        Args:
            max_age_minutes: 最大任务年龄（分钟）
            max_recovery_count: 最大恢复任务数量

        Returns:
            恢复结果统计
        """
        app_logger.info("开始检查和恢复中断的任务...")

        # 查找中断的任务
        interrupted_tasks = self.find_interrupted_tasks(max_age_minutes)

        if not interrupted_tasks:
            app_logger.info("未发现需要恢复的中断任务")
            return {"total_found": 0, "recovered": 0, "failed": 0, "skipped": 0}

        app_logger.info(f"发现 {len(interrupted_tasks)} 个中断任务")

        # 限制恢复数量，避免一次性恢复太多任务
        tasks_to_recover = interrupted_tasks[:max_recovery_count]
        if len(interrupted_tasks) > max_recovery_count:
            app_logger.warning(f"中断任务数量过多，只恢复前 {max_recovery_count} 个")

        recovered_count = 0
        failed_count = 0

        for task_info in tasks_to_recover:
            task_id = task_info["task_id"]
            age_hours = task_info["age_hours"]

            app_logger.info(f"恢复任务 {task_id[:8]}... (中断 {age_hours:.1f} 小时)")
            app_logger.info(f"  文件: {task_info['file_key']}")
            app_logger.info(f"  阶段: {task_info['current_stage']}")
            app_logger.info(f"  进度: {task_info['progress']}%")

            if self.recover_task(task_info):
                recovered_count += 1
                app_logger.info(f"✅ 任务 {task_id[:8]}... 恢复成功")
            else:
                failed_count += 1
                app_logger.error(f"❌ 任务 {task_id[:8]}... 恢复失败")

        result = {
            "total_found": len(interrupted_tasks),
            "recovered": recovered_count,
            "failed": failed_count,
            "skipped": len(interrupted_tasks) - len(tasks_to_recover),
        }

        app_logger.info(
            f"任务恢复完成: 发现 {result['total_found']} 个，"
            f"恢复 {result['recovered']} 个，"
            f"失败 {result['failed']} 个，"
            f"跳过 {result['skipped']} 个"
        )

        return result


def run_task_recovery():
    """运行任务恢复（可以作为独立脚本调用）"""
    recovery = TaskRecovery()
    return recovery.recover_all_interrupted_tasks()


if __name__ == "__main__":
    # 作为独立脚本运行
    import os
    import sys

    # 添加项目根目录到 Python 路径
    sys.path.insert(0, os.path.dirname(os.path.dirname(os.path.abspath(__file__))))

    # 设置环境变量
    os.environ.setdefault("DISABLE_NACOS", "true")

    result = run_task_recovery()
    print(f"恢复结果: {result}")
