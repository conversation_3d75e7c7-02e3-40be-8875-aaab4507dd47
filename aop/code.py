import json

def main(arg1: list) -> dict:
    result = {}
    citation_counter = 1
    total_q_length = 0
    max_q_length = 20000
    exceeded = False

    for item in arg1:
        parsed = json.loads(item)
        data_list = parsed.get("data", {}).get("list", [])

        for data_item in data_list:
            q = data_item.get("q", "")
            q_length = len(q)

            # 如果这条加上去会超过限制，并且之前还没超限过
            if total_q_length + q_length > max_q_length:
                # 加上这条，作为“最后一条”
                result[f"citation{citation_counter}"] = {
                    "q": q,
                    "collectionId": data_item.get("collectionId", ""),
                    "sourceId": data_item.get("sourceId", ""),
                    "chunkId":data_item.get("id", ""),
                    "sourceName": data_item.get("sourceName", ""),
                    "positions": data_item.get("positions", ""),
                    "score": data_item.get("score", "")
                }
                exceeded = True
                break  # 停止添加更多引用

            # 正常添加当前引用
            result[f"citation{citation_counter}"] = {
                "q": q,
                "collectionId": data_item.get("collectionId", ""),
                "sourceId": data_item.get("sourceId", ""),
                "chunkId":data_item.get("id", ""),
                "sourceName": data_item.get("sourceName", ""),
                "positions": data_item.get("positions", ""),
                "score": data_item.get("score", "")
            }
            total_q_length += q_length
            citation_counter += 1

        if exceeded:
            break  # 外层也结束

    return {"result": result}

if __name__ == '__main__':
    arg1=[
    "{\"requestId\": \"deb94fd05ad842989aa7b5fe3dcdce2e\", \"code\": 200, \"data\": {\"duration\": \"0.779s\", \"limit\": 3, \"list\": [{\"id\": \"597613687dd4d27f\", \"q\": \"[总结/Summary] 该研究探讨了利用生成式AI技术（如GPT-4、T5和BioBART）优化临床试验方案编写的创新方法。通过分析II型糖尿病试验协议，研究团队从多个数据源提取元数据，经ETL流程处理后用于模型训练。实验采用T5、BioBART及GPT系列模型生成协议内容，其中GPT-4在文本质量和连贯性上表现最优。评估指标（如ROUGE、BLEU）显示，提供示例可显著提升生成效果，两例提示时效果最佳。\\n\\n研究重点包括：1）数据处理阶段整合临床试验关键字段（如MeSH术语、治疗方案）；2）模型对比显示GPT-4-1106-preview在长文本生成中的优势；3）案例验证中，长效胰岛素LY3209590的试验设计成功生成，证实AI可准确捕捉药物机制与试验细节。项目证实生成式AI能提升协议编写效率，未来将扩展自动化范围并优化模型精度。参考文献涵盖MASS预训练、知识嵌入等前沿技术，为临床研究创新奠定基础。\", \"datasetId\": \"fb3ea9c27bd711f09c06a3ba766a0fc7\", \"collectionId\": \"228b18c67bd811f0a074ef0790a93eb2\", \"sourceName\": \"LLM临床试验方案写作.pdf\", \"score\": [{\"type\": \"bge-reranker-large___OpenAI-API\", \"value\": \"0.35935882\"}], \"tokens\": 408, \"positions\": [], \"termSimilarity\": 0.084798306, \"vectorSimilarity\": 1.0}, {\"id\": \"c4da25eed23af6cd\", \"q\": \"2.6 MethodologyIn order to carry out the task of protocol generation, various activities from study and drug metadata collection, clinical trials protocol introduction and study design sections extraction, data preprocessing. prompt engineering and evaluation of the final results.· Protocol and Section Review: Analyzing Type II diabetes protocols from CT.GOV, focusing on US-based sponsors and recent studies. · Section Selection for Automation: Choosing the Introduction and Study Design sections based on their prevalence and significance in protocols. · Metadata Collection: Accumulating extensive metadata from CT.Gov and TrialTrove for the selected protocols.· Technical Implementation:- LLM Training: Experimentation with T5 models, leading to insights about the chal-lenges in generating satisfactory outputs. - GPT-4 Prompt Engineering: Successfully leveraging GPT-4 for high-quality generation of the Introduction section.2.7 Data Sources and Analysis2.7.1 Drug level metadataThe drug level metadata formed a foundational component of our AI model's training data. Thedataset encompassed comprehensive details about each drug, including:· Basic Information: Citeline Drug ID, Generic Drug Name, and a summary of the drug. · Development Status: Detailed information on the development stage of the drug, including global and regional statuses. · Therapeutic Application: Insight into the diseases targeted by the drug, including whether these diseases are categorized as rare.· Company Profiles: Information about the companies involved in the development and marketing of the drugs, including headquarters and subsidiary details.· Clinical and Scientific Details: In-depth information on the drug's mechanism of action,therapeutic class, delivery route, and molecular characteristics.This metadata provided valuable insights into the drugs’ profiles, significantly aiding in the generation of contextually accurate and relevant protocol sections.2.7.2 Study level metadataThe study level metadata enriched our dataset with detailed information on various clinical trials.This data included:· Trial Information: Trial ID, title, phase, status, and detailed objectives. · Sponsorship Details: Information on the sponsor companies and their geographical locations · Patient and Disease Information: Detailed descriptions of the patient segments, diseases studied, and inclusion/exclusion criteria. · Endpoints and Outcomes: Information on primary and secondary endpoints, including details and dates of reporting.This diverse dataset enabled the AI model to understand the nuances of different clinical trials. ensuring that the generated protocol sections were aligned with the specific requirements of each study. The metadata fields used in this project are summarized in Table 1.Metadata fields used in model training and prompt engineering:2.8 Data Sources\", \"datasetId\": \"fb3ea9c27bd711f09c06a3ba766a0fc7\", \"collectionId\": \"228b18c67bd811f0a074ef0790a93eb2\", \"sourceName\": \"LLM临床试验方案写作.pdf\", \"score\": [{\"type\": \"bge-reranker-large___OpenAI-API\", \"value\": \"0.03563059\"}], \"tokens\": 2869, \"positions\": [[3, 106, 504, 388, 422], [3, 133, 531, 432, 575], [3, 106, 504, 609, 619], [3, 108, 506, 627, 651], [3, 132, 530, 658, 723], [4, 133, 531, 74, 123], [4, 107, 505, 132, 154], [4, 106, 504, 165, 177], [4, 108, 506, 184, 207], [4, 132, 530, 217, 297], [4, 107, 505, 306, 340], [4, 106, 504, 343, 357]], \"termSimilarity\": 0.049682334, \"vectorSimilarity\": 0.0028431923}, {\"id\": \"50c854e9e00a2160\", \"q\": \"The summary data, metadata, and code for this work can be found in the following GitHub repository:https://github.com/mprtrmrtz/protocol_authoring_using_LLM2.9 Data processing and PreparationThe data processing for this project involved a comprehensive ETL process. This process was meticulously designed to extract relevant data from three key sources: protocol extractions, study metadata, and drug metadata. The primary goal was to prepare this data for effective use in training and utilizing the generative AI models for protocol generation.2.9.1Data Extraction· Protocol Extractions: The ‘protocol_extractions.xlsx^ file was read into a DataFrame, cap-turing essential information about various clinical trial protocols · Study Metadata: Similarly, data from 'TT Study Level Metadata.xlsx' was loaded, providing detailed metadata on clinical studies.· Drug Metadata: The “TT Drug Level Metadata.xlsx' file was processed to obtain compre-hensive data on drugs involved in these studies. 2.9.2 Data Pre-processing and Transformation· Field Selection: Key fields were identified for both study and drug metadata to ensurerelevancy to the project's objectives. Fields such as 'Generic Drug Name', 'Drug Disease',and 'Event Date’ for drugs, and 'Trial ID', 'Disease', and 'Therapeutic Area’ for studies,were among those selected.· Duplicate Removal: A custom function ‘remove_duplicates' was used to eliminate duplicate entries within specific columns of the drug metadata, ensuring data cleanliness. - Data Splitting and Exploding: In the study metadata, the 'Primary Tested Drug’ column was split and exploded to disaggregate the drug names, facilitating more detailed analysis.· Text Replacement: Specific text replacements were made in the 'Primary Tested Drug'’ field to standardize drug names, enhancing data uniformity.· Dataframe Renaming and Merging: The drug metadata DataFrame was renamed for consis-tency, and then merged with the study metadata to create a combined DataFrame, aligning drugs with their corresponding trials.· NCT ID Extraction: The National Clinical Trial (NCT) ID was extracted from the 'Proto-col/Trial ID' column, which was a critical step to align each trial with its specific protocol. · Final Data Merge: The combined study and drug metadata were merged with the protocol extractions DataFrame using the NCT ID, culminating in a comprehensive dataset that aligns protocol extractions with detailed study and drug information2.9.3Load PhaseThe processed data was manually provided to the models in this work, however, in the actual implementation, it is is expected that the processed data would be provided through AWS or Database query for further analysis.\", \"datasetId\": \"fb3ea9c27bd711f09c06a3ba766a0fc7\", \"collectionId\": \"228b18c67bd811f0a074ef0790a93eb2\", \"sourceName\": \"LLM临床试验方案写作.pdf\", \"score\": [{\"type\": \"bge-reranker-large___OpenAI-API\", \"value\": \"0.0348684\"}], \"tokens\": 2695, \"positions\": [[4, 106, 505, 388, 412], [4, 106, 505, 444, 489], [4, 133, 532, 528, 604], [4, 106, 505, 614, 626], [4, 134, 533, 632, 676], [4, 134, 533, 679, 725], [6, 133, 532, 74, 197], [6, 107, 506, 239, 272]], \"termSimilarity\": 0.049812, \"vectorSimilarity\": 0.0}], \"docAggs\": [{\"count\": 3, \"docId\": \"228b18c67bd811f0a074ef0790a93eb2\", \"docName\": \"LLM临床试验方案写作.pdf\"}]}, \"success\": true}",
    "{\"requestId\": \"ecd9739761dc49d5a173e0a99782c842\", \"code\": 200, \"data\": {\"duration\": \"0.849s\", \"limit\": 5, \"list\": [{\"id\": \"597613687dd4d27f\", \"q\": \"[总结/Summary] 该研究探讨了利用生成式AI技术（如GPT-4、T5和BioBART）优化临床试验方案编写的创新方法。通过分析II型糖尿病试验协议，研究团队从多个数据源提取元数据，经ETL流程处理后用于模型训练。实验采用T5、BioBART及GPT系列模型生成协议内容，其中GPT-4在文本质量和连贯性上表现最优。评估指标（如ROUGE、BLEU）显示，提供示例可显著提升生成效果，两例提示时效果最佳。\\n\\n研究重点包括：1）数据处理阶段整合临床试验关键字段（如MeSH术语、治疗方案）；2）模型对比显示GPT-4-1106-preview在长文本生成中的优势；3）案例验证中，长效胰岛素LY3209590的试验设计成功生成，证实AI可准确捕捉药物机制与试验细节。项目证实生成式AI能提升协议编写效率，未来将扩展自动化范围并优化模型精度。参考文献涵盖MASS预训练、知识嵌入等前沿技术，为临床研究创新奠定基础。\", \"datasetId\": \"fb3ea9c27bd711f09c06a3ba766a0fc7\", \"collectionId\": \"228b18c67bd811f0a074ef0790a93eb2\", \"sourceName\": \"LLM临床试验方案写作.pdf\", \"score\": [{\"type\": \"bge-reranker-large___OpenAI-API\", \"value\": \"0.3000395\"}], \"tokens\": 408, \"positions\": [], \"termSimilarity\": 5.642508e-05, \"vectorSimilarity\": 1.0}, {\"id\": \"d6019549a645e8e7\", \"q\": \"ROUGE (Recall-Oriented Understudy for Gisting Evaluation) is a set of metrics used for evaluating automatic summarization and machine translation.It operates in the following paradigm:· ROUGE-N: Measures the overlap of n-grams between the generated text and reference text. · ROUGE-L: Considers the longest common subsequence between the generated and reference text. · Recall: The proportion of the reference text's n-grams found in the generated text. · Precision: The proportion of the generated text's n-grams that are found in the reference text. · F1 Score: The harmonic mean of precision and recall, providing a balance between the two. · ROUGE is particularly useful in evaluating summarization where capturing the essence of the original text is crucial. · A higher ROUGE score indicates that the generated text more effectively captures the key points of the reference text.These metrics are valuable tools in assessing the quality of text generated by AI models. They provide objective ways to measure aspects like similarity, fuency, and relevance of the generated text compared to a human-written reference.Figures 1 - 7 and Table 2 show the results of analysis on model performance in the task of Introduction section generation for all models and variation of number of examples.\", \"datasetId\": \"fb3ea9c27bd711f09c06a3ba766a0fc7\", \"collectionId\": \"228b18c67bd811f0a074ef0790a93eb2\", \"sourceName\": \"LLM临床试验方案写作.pdf\", \"score\": [{\"type\": \"bge-reranker-large___OpenAI-API\", \"value\": \"0.10903039\"}], \"tokens\": 1294, \"positions\": [[9, 106, 504, 314, 338], [9, 132, 530, 363, 509], [9, 106, 504, 518, 552], [9, 107, 505, 566, 590]], \"termSimilarity\": 0.10588372, \"vectorSimilarity\": 0.11637261}, {\"id\": \"517ba63c56aee407\", \"q\": \"2.9.4 Data Preparation for Model TrainingThe final merged dataset, now rich with both protocol and associated metadata, was primed for use in training the generative AI models. This dataset provides the depth and context necessary for the AI to understand and generate accurate protocol sections.The ETL process employed in this project was critical in ensuring that the data was not only comprehensive and relevant but also clean and structured in a way that maximizes its utility in training effective Al models. This meticulous approach to data processing underpins the success of the AI model in generating precise and contextually appropriate clinical trial protocols.3  Model Development and EvaluationAfter data preprocessing step and preparation of metadata for text generation, two approaches weretaken for text generation:1. LLM Model Training: Involving T5 Small, Large and BioBart models training 2. Open AI GPT Models Prompt Engineering: Involving gpt-3.5, gpt-4 and their variations3.1 Model Training Based Google LLM ModelsThe following models were used for training on the metadata and their corresponding Introductionsection of the protocol. Additional information about the models below:1. T5 Small LLM Model· Model Overview: T5, or Text-to-Text Transfer Transformer, is a language model developed by Google. The \\\"small\\\" variant is a more compact version of the standard T5 model. · Model Size: The T5 small model typically has fewer parameters compared to its larger counterparts. The exact number can vary but is significantly lower than the base or large models.· Key Features:- Designed to convert all NLP tasks into a text-to-text format, meaning both the input and output are always text strings.Despite being a smaller variant, it retains the core functionality of the T5 architec-ture and is suitable for environments where computational resources are limited. - Ideal for tasks like translation, summarization, question answering, and more, but with a balance between performance and resource efficiency.2. T5 Large LLM Model· Model Overview: This is a larger variant of the T5 model, which means it has more parameters and potentially better performance at the cost of increased computational resource requirements. · Model Size: The T5 large model has significantly more parameters than the small variant, enhancing its capacity to understand and generate text.· Key Features:- Like the small model, it treats all NLP tasks as text-to-text problems. - Due to its larger size, it generally performs better on complex tasks and can handle more nuanced aspects of language processing. - Suitable for high-resource settings where advanced NLP capabilities are needed.\", \"datasetId\": \"fb3ea9c27bd711f09c06a3ba766a0fc7\", \"collectionId\": \"228b18c67bd811f0a074ef0790a93eb2\", \"sourceName\": \"LLM临床试验方案写作.pdf\", \"score\": [{\"type\": \"bge-reranker-large___OpenAI-API\", \"value\": \"0.041162334\"}], \"tokens\": 2692, \"positions\": [[6, 106, 506, 294, 306], [6, 106, 506, 314, 347], [6, 105, 505, 346, 392], [6, 105, 505, 429, 454], [6, 129, 529, 462, 473], [6, 128, 528, 477, 491], [6, 107, 507, 502, 513], [6, 108, 508, 523, 546], [6, 130, 530, 555, 566], [6, 152, 552, 571, 721], [7, 129, 529, 74, 85], [7, 153, 553, 90, 207]], \"termSimilarity\": 0.050283287, \"vectorSimilarity\": 0.01988011}, {\"id\": \"c4da25eed23af6cd\", \"q\": \"2.6 MethodologyIn order to carry out the task of protocol generation, various activities from study and drug metadata collection, clinical trials protocol introduction and study design sections extraction, data preprocessing. prompt engineering and evaluation of the final results.· Protocol and Section Review: Analyzing Type II diabetes protocols from CT.GOV, focusing on US-based sponsors and recent studies. · Section Selection for Automation: Choosing the Introduction and Study Design sections based on their prevalence and significance in protocols. · Metadata Collection: Accumulating extensive metadata from CT.Gov and TrialTrove for the selected protocols.· Technical Implementation:- LLM Training: Experimentation with T5 models, leading to insights about the chal-lenges in generating satisfactory outputs. - GPT-4 Prompt Engineering: Successfully leveraging GPT-4 for high-quality generation of the Introduction section.2.7 Data Sources and Analysis2.7.1 Drug level metadataThe drug level metadata formed a foundational component of our AI model's training data. Thedataset encompassed comprehensive details about each drug, including:· Basic Information: Citeline Drug ID, Generic Drug Name, and a summary of the drug. · Development Status: Detailed information on the development stage of the drug, including global and regional statuses. · Therapeutic Application: Insight into the diseases targeted by the drug, including whether these diseases are categorized as rare.· Company Profiles: Information about the companies involved in the development and marketing of the drugs, including headquarters and subsidiary details.· Clinical and Scientific Details: In-depth information on the drug's mechanism of action,therapeutic class, delivery route, and molecular characteristics.This metadata provided valuable insights into the drugs’ profiles, significantly aiding in the generation of contextually accurate and relevant protocol sections.2.7.2 Study level metadataThe study level metadata enriched our dataset with detailed information on various clinical trials.This data included:· Trial Information: Trial ID, title, phase, status, and detailed objectives. · Sponsorship Details: Information on the sponsor companies and their geographical locations · Patient and Disease Information: Detailed descriptions of the patient segments, diseases studied, and inclusion/exclusion criteria. · Endpoints and Outcomes: Information on primary and secondary endpoints, including details and dates of reporting.This diverse dataset enabled the AI model to understand the nuances of different clinical trials. ensuring that the generated protocol sections were aligned with the specific requirements of each study. The metadata fields used in this project are summarized in Table 1.Metadata fields used in model training and prompt engineering:2.8 Data Sources\", \"datasetId\": \"fb3ea9c27bd711f09c06a3ba766a0fc7\", \"collectionId\": \"228b18c67bd811f0a074ef0790a93eb2\", \"sourceName\": \"LLM临床试验方案写作.pdf\", \"score\": [{\"type\": \"bge-reranker-large___OpenAI-API\", \"value\": \"0.040672146\"}], \"tokens\": 2869, \"positions\": [[3, 106, 504, 388, 422], [3, 133, 531, 432, 575], [3, 106, 504, 609, 619], [3, 108, 506, 627, 651], [3, 132, 530, 658, 723], [4, 133, 531, 74, 123], [4, 107, 505, 132, 154], [4, 106, 504, 165, 177], [4, 108, 506, 184, 207], [4, 132, 530, 217, 297], [4, 107, 505, 306, 340], [4, 106, 504, 343, 357]], \"termSimilarity\": 0.051393863, \"vectorSimilarity\": 0.015654802}, {\"id\": \"5ef6426c4083e646\", \"q\": \"3. BioBART LLM Model· Model Overview: BioBART is a variant of the BART (Bidirectional and Auto-Regressive Transformers) model, which is tailored for biomedical text.· Key Features:- BART is known for its effectiveness in text generation and comprehension tasks,and BioBART adapts this for the biomedical domain. - It's pre-trained on large-scale biomedical literature, making it adept at handling specialized terminology and concepts found in medical texts.- Often used for tasks like biomedical text summarization, medical question answer-ing, and information extraction from clinical notes or research papers.The T5 and BioBART models are examples of how language models can be adapted to different scales (like small and large) and specialized domains (like biomedicine). These adaptations allow for more effective application in specific scenarios, balancing the trade-offs between computational resources and task-specific performance.It was realized that the text generation task using these models were not efficient and accurate at all,the output generated text was short (400 characters) and did not encapsulate the full purpose of text generation in this project. The work on this venue was immediately halted and a prompt engineering path was explored as an alternative approach.3.2 Open AI GPT Models1. GPT-3.5● Model Variants: GPT-3.5-turbo, GPT-3.5-turbo-1106, GPT-3.5-turbo-16k-0613· Key Features:Iterative improvements on GPT-3, focusing on efficiency, speed, and fine-tuning capabilities.- The 'turbo’ variants indicate models optimized for faster response times and effi-cient processing, suitable for interactive applications. - These models continue to push the boundaries of language understanding andgeneration, maintaining high performance across diverse tasks. 2. GPT-4· Key Features:- Anticipated to be an advancement over GPT-3 with more parameters and improved capabilities. - Expected to further enhance the model's understanding. context handling, and response generation. - Likely to continue OpenAI's trend of creating more powerful and versatile languagemodels. 3. GPT-4-1106-preview applications or testing.· Key Features:- A variant of GPT-4, which is a preview or a scaled-down version for specific- Likely to offer improvements over GPT-3.5 while being a step towards the full GPT-4 model.Each iteration of GPT models represents a leap forward in natural language processing, with im-provements in text generation, comprehension, and versatility in handling various language-based tasks. As AI research progresses, these models continue to evolve, offering increasingly advanced capabilities in understanding and generating human language.\", \"datasetId\": \"fb3ea9c27bd711f09c06a3ba766a0fc7\", \"collectionId\": \"228b18c67bd811f0a074ef0790a93eb2\", \"sourceName\": \"LLM临床试验方案写作.pdf\", \"score\": [{\"type\": \"bge-reranker-large___OpenAI-API\", \"value\": \"0.035274055\"}], \"tokens\": 2675, \"positions\": [[7, 129, 530, 210, 221], [7, 153, 553, 225, 330], [7, 106, 506, 340, 384], [7, 105, 506, 387, 433], [7, 106, 506, 446, 457], [7, 129, 530, 462, 473], [7, 153, 553, 477, 500], [7, 165, 565, 500, 561], [7, 130, 531, 561, 584], [7, 153, 553, 589, 660], [7, 129, 530, 659, 724], [7, 153, 554, 689, 713], [8, 165, 565, 71, 96], [8, 105, 506, 104, 151]], \"termSimilarity\": 0.05039151, \"vectorSimilarity\": 0.0}], \"docAggs\": [{\"count\": 5, \"docId\": \"228b18c67bd811f0a074ef0790a93eb2\", \"docName\": \"LLM临床试验方案写作.pdf\"}]}, \"success\": true}"
  ]
    print(main(arg1))