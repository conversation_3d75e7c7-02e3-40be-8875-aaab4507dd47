import asyncio
import concurrent.futures
import json
from typing import List

from configurer.config_reader import get_vl_config
import aiohttp

# from configurer.yy_nacos import init_nacos_manager
from models.file_info import FileInfo

# 全局线程池
GLOBAL_EXECUTOR = concurrent.futures.ThreadPoolExecutor(max_workers=10)


# 异步发送请求函数
async def send_request(session, url):
    d = get_vl_config()
    base_url = d.get('base_url', 'https://oneapi.yiya-ai.com/v1')
    api_key = d.get('api_key', 'sk-jPcR81jr5nsp03sv7b3a19D1DdAc4e6f9604Ae8c3b025567')
    model = d.get('model', 'olmOCR-7B-0225')
    temperature = d.get('temperature', 0.1)
    top_p = d.get('top_p', 0.5)
    max_tokens = d.get('max_tokens', 4096)
    seed = d.get('seed', 1683806810)
    stream = d.get('stream', False)
    prompt = d.get('prompt', '提取图片中文字，并以markdown格式输出，且保留图片格式，只保留原文，不需要输出其他说明性的文字，也不用返回```markdown相关文字')

    body = {
        "model": f"{model}",
        "messages": [
            {
                "role": "user",
                "content": [
                    {
                        "type": "text",
                        "text": prompt
                    },
                    {
                        "type": "image_url",
                        "image_url": {
                            "url": f"{url}"
                        }
                    }
                ]
            }
        ],
        "temperature": temperature,
        "top_p": top_p,
        "seed": seed,
        "stream": stream,
        "max_tokens": max_tokens
    }

    headers = {
        "Content-Type": "application/json",
        "Authorization": f"Bearer {api_key}",
        "Accept": "application/json"
    }

    try:
        async with session.post(base_url, json=body, headers=headers) as resp:
            async for chunk in resp.content:
                if chunk:
                    chunk_data = chunk.decode('utf-8')
                    if chunk_data.startswith('data: '):
                        chunk_data = chunk_data[6:]
                    try:
                        chunk_data = json.loads(chunk_data)
                        return chunk_data
                    except json.JSONDecodeError:
                        continue
    except Exception as e:
        print(f"请求失败: {e}")
        return None


# 批量请求函数
async def batch_request(urls):
    async with aiohttp.ClientSession() as session:
        tasks = [send_request(session, url) for url in urls]
        results = await asyncio.gather(*tasks)
    return results


# 单张图片转换函数
def img2md(url):
    urls = [url]
    results = asyncio.run(batch_request(urls))
    return results[0] if results else None


# 使用线程池并发执行多张图片转换
def convert_images_concurrently(urls):
    # 如果 urls 为空，直接返回空列表
    if not urls:
        print("Input URLs list is empty, returning an empty list.")
        return []

    results = []
    failed_tasks = []

    # 提交每个 img2md 调用到全局线程池，并记录每个任务的 URL 和 Future 对象
    futures = {GLOBAL_EXECUTOR.submit(img2md, url): url for url in urls}

    # 获取结果并处理异常
    for future in concurrent.futures.as_completed(futures):
        url = futures[future]
        try:
            result = future.result()
            result['url'] = url
            results.append(result)
            print(f"Successfully processed URL: {url}")
        except Exception as e:
            print(f"Failed to process URL {url}: {e}")
            failed_tasks.append(url)

    # 确保结果顺序与输入 urls 的顺序一致
    ordered_results = [None] * len(urls)
    for idx, url in enumerate(urls):
        for result in results:
            if result.get('url') == url:  # 假设 img2md 返回的结果包含 'url' 字段
                ordered_results[idx] = result
                break

    if failed_tasks:
        print(f"Some tasks failed: {failed_tasks}")

    return ordered_results


# 合并所有文档成一个markdown
def convert_images_and_merge(urls):
    results = convert_images_concurrently(urls)
    all_pages_md = []

    # 过滤 None
    results = [result for result in results if result is not None]

    for idx, result in enumerate(results):
        # 为区分不同页，可以加上简单标题
        content = result["choices"][0]["message"]["content"]
        page_md_formatted = f"## Page {idx + 1}\n\n{content}"
        all_pages_md.append(page_md_formatted)

    # 最终将每一页的内容按顺序合并
    final_markdown = "\n\n".join(all_pages_md)
    return final_markdown


def convert_local_images_and_merge(imgs: List[FileInfo]):
    if not imgs:
        return ""

    from servers.file_server import upload_local_file, get_file_url

    img_urls = []

    for img in imgs:
        file_key = upload_local_file(img.file_path, img.file_name)
        img_urls.append(get_file_url(file_key))

    return convert_images_and_merge(img_urls)


if __name__ == '__main__':
    # init_nacos_manager()
    # 测试图片 URL 列表
    image_urls = [
        'https://yiya-common.oss-cn-hangzhou.aliyuncs.com/pdf/test-bingli.jpg',
        # 'https://yiya-common.oss-cn-hangzhou.aliyuncs.com/pdf/test-bingli.jpg',
        # 'https://yiya-common.oss-cn-hangzhou.aliyuncs.com/pdf/test-bingli.jpg'
    ]

    # 开始计时
    start_time = asyncio.get_event_loop().time()

    # 并发转换多张图片
    # results = convert_images_and_merge(image_urls)
    #
    # # 输出结果和耗时
    # for i, result in enumerate(results):
    #     print(f"Image {i + 1} Result: {result}")

    print(convert_images_and_merge(image_urls))

    print('Time consumed:', asyncio.get_event_loop().time() - start_time)
