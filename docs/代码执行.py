import json

def main(files: str) -> dict:
    if not files:
        return {'result': None}
    try:
        loaded_files = json.loads(files)
        return {'result': loaded_files[0]['file_url']}
    except (json.<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>r, <PERSON><PERSON><PERSON>r, IndexError):
        return {'result': None}

if __name__ == '__main__':
    a="{\"success\":true,\"code\":0,\"message\":null,\"result\":[{\"file_name\":\"page_1.png\",\"file_key\":\"oss-20250828111318-page_1.png\",\"file_url\":\"https://yiya-dev.oss-cn-hangzhou.aliyuncs.com/oss-20250828111318-page_1.png?OSSAccessKeyId=LTAI5tSMtzYQ5GVPCm8njDYp&Expires=1756955598&Signature=4PZKmW8r2D7G1T2hjCRUaQuPDGE%3D\",\"file_path\":null},{\"file_name\":\"page_2.png\",\"file_key\":\"oss-20250828111318-page_2.png\",\"file_url\":\"https://yiya-dev.oss-cn-hangzhou.aliyuncs.com/oss-20250828111318-page_2.png?OSSAccessKeyId=LTAI5tSMtzYQ5GVPCm8njDYp&Expires=1756955598&Signature=KoPdVRI215TSjsiu8zybVUsVNCQ%3D\",\"file_path\":null},{\"file_name\":\"page_3.png\",\"file_key\":\"oss-20250828111318-page_3.png\",\"file_url\":\"https://yiya-dev.oss-cn-hangzhou.aliyuncs.com/oss-20250828111318-page_3.png?OSSAccessKeyId=LTAI5tSMtzYQ5GVPCm8njDYp&Expires=1756955599&Signature=Ie3T0syQGq8DRyO1JHMovQAz%2FT4%3D\",\"file_path\":null}],\"time_cost\":0}"
    print(main(a))