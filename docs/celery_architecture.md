# Celery 架构设计文档

## 🏗️ 架构概览

### 设计原则
- **单一职责**：每个组件只负责自己的核心功能
- **Circus主导**：所有进程管理由Circus统一负责
- **多层兜底**：提供多层故障恢复机制
- **避免冲突**：消除多套管理机制的冲突

### 架构图
```
Circus进程管理器
├── 主应用 Watcher (gunicorn)
├── Celery Worker Watcher (单一进程池Worker)
└── 健康监控 Watcher (监控和诊断)
```

## 🔧 组件职责

### 1. Circus (主管理器)
**职责**：
- ✅ 唯一的进程管理器
- ✅ 自动启动和重启Worker
- ✅ 进程生命周期管理
- ✅ 故障检测和恢复

**配置**：
```ini
[watcher:yiya-ai-bot-celery]
autostart=true
autorestart=true
respawn_limit=3
```

### 2. CeleryManager (监控助手)
**职责**：
- ✅ 健康检查和诊断
- ✅ 进程信息收集
- ✅ 请求Circus重启
- ✅ 紧急情况下的异常进程清理
- ❌ ~~直接启动Worker~~（已移除）

**核心方法**：
- `get_circus_managed_workers()` - 获取Circus管理的Worker
- `request_circus_restart()` - 请求Circus重启
- `emergency_cleanup()` - 紧急清理异常进程

### 3. CeleryHealthMonitor (简化监控)
**职责**：
- ✅ 基础连接检查
- ✅ Worker存活检测
- ✅ 真正故障时触发Circus重启
- ❌ ~~复杂的性能指标收集~~（已移除）

**监控指标**（简化）：
- Celery Broker连接状态
- Worker是否存在和响应
- 只在真正无法连接时重启

## 🛡️ 兜底机制

### 多层故障恢复
1. **第一层：Circus自动重启**
   - Worker进程异常退出时自动重启
   - 配置：`autorestart=true`, `respawn_limit=3`

2. **第二层：健康监控触发重启**
   - 检测到健康问题时请求Circus重启
   - 方法：`celery_manager.request_circus_restart()`

3. **第三层：紧急清理模式**
   - Circus重启失败时清理异常进程
   - 等待Circus自动重启正常Worker

4. **第四层：K8s Pod重启**
   - 所有机制都失败时，K8s重启整个Pod
   - 配置：`restartPolicy: Always`

## 🚫 已移除的冲突功能

### CeleryManager
- ❌ `start_worker()` - 直接启动Worker（与Circus冲突）
- ❌ `kill_all_celery_workers()` - 无差别清理（改为智能清理）

### CeleryHealthMonitor
- ❌ 直接启动Worker的重启逻辑
- ❌ 绕过Circus的进程管理

## 📋 最佳实践

### 部署建议
1. **单容器部署**：避免多Worker竞争
2. **使用进程池**：`--pool=prefork`（支持超时和强制终止）
3. **依赖Circus管理**：不要手动启动Worker

### 监控建议
1. **关注健康分数**：低于阈值时会触发重启
2. **监控Circus日志**：了解重启原因
3. **检查架构完整性**：使用`architecture_test.py`

### 故障排查
1. **检查Worker状态**：`kubectl exec pod -- ps aux | grep celery`
2. **查看Circus日志**：`kubectl logs pod`
3. **运行架构测试**：`python utils/architecture_test.py`

## 🔍 验证命令

```bash
# 检查Worker进程
kubectl exec pod -- ps aux | grep celery

# 检查Circus状态
kubectl exec pod -- circusctl status

# 运行架构测试
kubectl exec pod -- python utils/architecture_test.py
```

## ⚠️ 注意事项

1. **不要手动启动Worker**：所有Worker应由Circus管理
2. **不要直接杀死Circus Worker**：使用`circusctl stop/start`而不是`restart`
3. **多容器部署需谨慎**：可能导致任务竞争和状态冲突
4. **监控健康分数**：50分以下才会触发重启，避免过度敏感
5. **重启间隔控制**：最小5分钟间隔，避免频繁重启导致Worker累积

## 🎯 架构优势

1. **职责清晰**：每个组件职责明确，无重叠
2. **故障恢复强**：多层兜底机制确保高可用
3. **易于维护**：统一的进程管理，减少复杂性
4. **性能稳定**：进程池比线程池更稳定可靠
