# Celery 故障排除指南

## 问题描述

Celery Worker 无法启动，导致任务无法执行，API 返回任务 ID 但查询状态时显示 `NOT_FOUND`。

## 根本原因

### 1. Celery Worker 启动失败
- **错误信息**: `Running a worker with superuser privileges when the worker accepts messages serialized with pickle is a very bad idea!`
- **原因**: Celery 配置中启用了 `pickle` 序列化，但以 root 用户运行时出于安全考虑拒绝启动
- **解决方案**: 移除 `pickle` 序列化支持，只使用 `json` 序列化

### 2. 监控系统异常
- **错误信息**: `AttributeError: 'NoneType' object has no attribute 'stop_monitoring'`
- **原因**: `alert_manager` 为 `None`，导致程序退出时清理失败
- **解决方案**: 添加空值检查

### 3. 缺少环境变量
- **问题**: Celery Worker 启动命令中缺少 `C_FORCE_ROOT=true`
- **解决方案**: 在启动命令中添加环境变量

## 修复内容

### 1. 修复 Celery 配置 (`celery_task/celeryconfig.py`)
```python
# 移除 pickle 支持，避免安全警告
accept_content = ["json"]   # 之前是 ["json", "pickle"]
```

### 2. 修复监控系统 (`utils/monitoring_system.py`)
```python
def stop_system_monitoring(self):
    """停止系统监控"""
    self._system_monitoring = False
    if self.alert_manager is not None:  # 添加空值检查
        self.alert_manager.stop_monitoring()
    app_logger.info("系统监控已停止")
```

### 3. 修复 Worker 启动配置 (`APP-META/docker-config/environment/common/app/conf/circus.ini`)
```bash
# 添加 C_FORCE_ROOT=true 环境变量
cmd=bash -c "POD_NAME=${HOSTNAME:-$(hostname)} && UNIQUE_NODE_NAME=\"celery@${POD_NAME}-$(date +%s)-$(shuf -i 1000-9999 -n 1)\" && export CELERY_NODE_NAME=\"$UNIQUE_NODE_NAME\" && C_FORCE_ROOT=true celery -A celery_task worker ..."
```

## 诊断工具

### 1. Celery 诊断脚本 (`scripts/diagnose_celery.py`)
```bash
python3 scripts/diagnose_celery.py
```
检查项目：
- Redis 连接状态
- Celery 配置
- Worker 状态
- 队列状态

### 2. Celery 连接测试 (`scripts/test_celery_connection.py`)
```bash
python3 scripts/test_celery_connection.py
```
测试项目：
- Celery broker 连接
- 任务提交功能

### 3. 重启 Celery Worker (`scripts/restart_celery.sh`)
```bash
bash scripts/restart_celery.sh
```
自动重启 Celery Worker 并检查状态

## 部署步骤

1. **应用配置修复**
   ```bash
   # 重新构建镜像
   docker build -t yiya-ai-bot:latest .
   ```

2. **重启服务**
   ```bash
   # 重启 Pod
   kubectl rollout restart deployment/yiya-ai-bot-dev
   ```

3. **验证修复**
   ```bash
   # 进入容器
   kubectl exec -it yiya-ai-bot-dev-xxx -- bash
   
   # 运行诊断
   python3 scripts/diagnose_celery.py
   
   # 检查 Worker 状态
   circusctl status yiya-ai-bot-celery
   
   # 查看日志
   tail -f logs/celery.log
   ```

## 验证方法

### 1. 检查 Worker 状态
```bash
circusctl status yiya-ai-bot-celery
```

### 2. 查看 Celery 日志
```bash
tail -f logs/celery.log
```

### 3. 测试 API
```bash
# 提交任务
curl --location 'http://dev-gw.yiya-ai.com/protocols/extract-by-docx' \
--header 'Content-Type: application/json' \
--data '{
    "protocol_file": {
        "file_name": "test.docx",
        "file_key": "test/protocol.docx"
    }
}'

# 查询任务状态
curl --location 'http://dev-gw.yiya-ai.com/protocols/task-status/{task_id}'
```

## 预防措施

1. **监控 Worker 状态**
   - 定期检查 `circusctl status`
   - 监控 Celery 日志

2. **配置验证**
   - 部署前运行诊断脚本
   - 验证 Redis 连接

3. **日志监控**
   - 设置日志告警
   - 定期检查错误日志

## 常见问题

### Q: Worker 启动后立即退出
A: 检查 Redis 连接和配置，运行诊断脚本

### Q: 任务提交成功但状态查询失败
A: 检查 Worker 是否正常运行，查看 Celery 日志

### Q: 内存使用过高
A: 检查 `worker_max_memory_per_child` 配置，适当调整内存限制 