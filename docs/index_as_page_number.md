# Word文档修订记录index改为页码功能

## 需求说明

将 `doc/single_diff/v3` API 返回的修订记录中的 `index` 字段从原来的递增数字（1,2,3...）改为基于Word分页标记计算出来的页码数字。

## 实现方案

### 核心思路

- **正文段落和表格**：`index` = 页码（基于Word分页标记计算）
- **页眉页脚**：`index` = 递增数字（保持原逻辑，因为页眉页脚出现在所有页面）

### 页码计算逻辑

利用Word XML中的分页标记：
1. `<w:lastRenderedPageBreak>` - Word自动插入的页面分隔标记
2. `<w:br w:type="page">` - 显式分页符

### 代码修改

**主要修改文件：`utils/docx_utils.py`**

1. **新增函数**：
   - `_calculate_page_mapping(paragraphs)` - 计算段落到页码的映射
   - `_estimate_table_page(table, paragraphs, page_mapping)` - 估算表格页码

2. **修改函数**：
   - `process_part_v1()` - 添加页码计算，将index改为页码

### 效果对比

**修改前**：
```json
{
    "type": "document",
    "original": "2摘要12",
    "revised": "2总结12", 
    "index": 3,  // 递增数字
    "topic": "",
    "source": "document.xml"
}
```

**修改后**：
```json
{
    "type": "document", 
    "original": "2摘要12",
    "revised": "2总结12",
    "index": 2,  // 页码数字（假设这个修订在第2页）
    "topic": "",
    "source": "document.xml"
}
```

## 测试结果

✅ **正文段落**：index = 页码（1, 2, 3...）
✅ **表格内容**：index = 页码（基于表格位置推断）
✅ **页眉页脚**：index = 递增数字（保持原逻辑）

## 使用说明

- 数据结构完全兼容，只是 `index` 的含义从"序号"变为"页码"
- 对于正文和表格，用户可以通过 `index` 直接知道修订在哪一页
- 页眉页脚由于出现在所有页面，仍使用递增序号

## 限制说明

- 页码基于Word分页标记推断，可能与实际渲染有细微差异
- 需要Word文档包含分页标记（大多数正常保存的文档都有）
- 表格页码通过位置推断，对于跨页表格可能不够精确
