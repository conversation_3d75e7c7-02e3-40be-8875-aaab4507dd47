# Celery 监控优化总结

## 优化前的问题

### 冗余功能分析
1. **任务恢复功能** - 100% 冗余（已禁用）
2. **OOM 检查** - 90% 冗余（只记录不行动）
3. **队列检查** - 80% 冗余（简单 ping 测试）
4. **健康分数计算** - 70% 冗余（过度复杂）
5. **Worker 健康检查** - 60% 冗余（重复逻辑）
6. **重启逻辑** - 50% 冗余（重复实现）
7. **进程检查** - 40% 冗余（重复收集）
8. **锁文件管理** - 30% 冗余（重复机制）

**总体冗余度：约 60-70%**

## 优化后的架构

### 保留的核心功能

#### 1. **CeleryHealthMonitor (简化版)**
- **功能**：统一健康监控入口
- **检查项目**：
  - Worker 状态检查（调用 celery_manager）
  - 内存使用监控
  - Redis 连接检查
- **行动**：触发 Circus 重启
- **检查频率**：60秒一次
- **重启条件**：连续5次健康分数 < 50

#### 2. **CeleryWorkerManager (简化版)**
- **功能**：进程管理和重启
- **核心方法**：
  - `get_celery_processes()` - 进程信息收集
  - `emergency_cleanup()` - 紧急清理
  - `request_circus_restart()` - Circus 重启
  - `check_worker_health()` - 基础健康检查
- **移除功能**：节点名冲突检查、连接质量检查

#### 3. **ConcurrencyManager (保留)**
- **功能**：并发控制和资源管理
- **使用场景**：任务限流、文件访问控制
- **依赖模块**：protocol_api.py, protocol_task.py, monitoring_system.py

#### 4. **DistributedLock (保留)**
- **功能**：分布式锁管理
- **使用场景**：任务状态更新、文件访问
- **依赖模块**：protocol_task.py, concurrency_manager.py

### 移除的冗余功能

#### 1. **任务恢复相关**
- `_check_stuck_tasks()` - 任务恢复检查
- `_check_oom_events()` - OOM 事件检查
- `task_recovery_enabled` - 任务恢复开关
- TaskRecovery 相关逻辑

#### 2. **复杂健康检查**
- `_check_queue_health()` - 队列健康检查
- `_calculate_health_score()` - 复杂权重计算
- `_check_periodic_restart()` - 定期重启检查
- 连接质量检查

#### 3. **重复的重启逻辑**
- `_trigger_circus_restart()` - 重复的重启方法
- `_is_restart_in_progress()` - 重启锁检查
- 重启锁文件管理

#### 4. **过度设计的监控**
- 分层检查频率控制
- 复杂的指标收集
- 多进程冲突检测

## 优化效果

### 代码简化
- **celery_health_monitor.py**: 从 617 行减少到约 300 行（减少 50%+）
- **celery_manager.py**: 从 361 行减少到约 250 行（减少 30%+）
- **总体代码量**: 减少约 40%

### 功能聚焦
- **健康检查**: 只检查关键指标（Worker状态、内存、Redis）
- **重启机制**: 统一使用 Circus 重启
- **监控逻辑**: 简化判断条件，减少误判

### 性能提升
- **检查频率**: 统一为 60 秒，减少资源消耗
- **重启阈值**: 连续 5 次失败才重启，避免频繁重启
- **锁机制**: 简化锁管理，减少文件 I/O

## 配置说明

### Circus 配置
```ini
[watcher:{{app_name}}-health-monitor]
cmd=bash -c "sleep 180 && python3.11 -c 'from utils.celery_health_monitor import start_health_monitoring; start_health_monitoring()'"
```

### 健康检查参数
- **检查间隔**: 60 秒
- **失败阈值**: 5 次
- **重启间隔**: 300 秒（5分钟）
- **健康分数阈值**: 50 分

### 监控指标权重
- **Worker 状态**: 50%
- **内存使用**: 30%
- **Redis 连接**: 20%

## 使用建议

### 1. **监控日志**
```bash
# 查看健康监控日志
kubectl logs -f <pod-name> | grep "健康监控"

# 查看健康分数
kubectl logs -f <pod-name> | grep "健康分数"
```

### 2. **手动重启**
```bash
# 进入 Pod
kubectl exec -it <pod-name> -- bash

# 使用 Circus 重启
circusctl restart yiya-ai-bot-celery
```

### 3. **健康状态检查**
```python
from utils.celery_health_monitor import get_health_status
status = get_health_status()
print(f"健康分数: {status['health_score']}/100")
```

## 注意事项

1. **任务恢复**: 已禁用自动任务恢复，依赖前端重新生成
2. **重启频率**: 设置了最小重启间隔，避免频繁重启
3. **锁文件**: 使用简单的文件锁防止多进程冲突
4. **日志级别**: 健康检查使用 DEBUG 级别，避免日志过多

## 后续优化建议

1. **监控指标**: 可考虑添加更多业务指标（如任务处理速度）
2. **告警机制**: 可集成外部告警系统
3. **配置化**: 将检查参数改为可配置
4. **指标导出**: 可考虑导出 Prometheus 指标 