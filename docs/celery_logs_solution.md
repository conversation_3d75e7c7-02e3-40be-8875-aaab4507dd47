# Celery 日志输出到容器日志的解决方案

## 问题描述

在容器化部署后，Celery 日志不显示在容器的日志中。通过 `kubectl logs -f xx` 只能看到 `application.log` 的内容，看不到 `celery.log` 的日志。

## 问题分析

1. **Circus 配置问题**：原来的配置使用了 `tee` 命令，可能影响日志流的正确输出
2. **日志流配置**：Circus 的 `StdoutStream` 配置可能没有正确捕获所有输出
3. **容器日志收集**：Docker/Kubernetes 只收集主进程的标准输出

## 解决方案

### 1. 修改 Circus 配置 (`circus.ini`)

```ini
[watcher:{{app_name}}-celery]
# 日志配置：使用Circus的日志流功能
stdout_stream.class=FileStream
stdout_stream.filename=/root/{{app_name}}/logs/celery.log
stdout_stream.max_bytes=10485760
stdout_stream.backup_count=5
stderr_stream.class=FileStream
stderr_stream.filename=/root/{{app_name}}/logs/celery_error.log
stderr_stream.max_bytes=10485760
stderr_stream.backup_count=5

# 启动命令：直接输出到标准输出，不使用tee
cmd=bash -c "POD_NAME=${HOSTNAME:-$(hostname)} && UNIQUE_NODE_NAME=\"celery@${POD_NAME}-$(date +%s)-$(shuf -i 1000-9999 -n 1)\" && export CELERY_NODE_NAME=\"$UNIQUE_NODE_NAME\" && celery -A celery_task worker --loglevel=info --pool=prefork --concurrency=4 --hostname=\"$UNIQUE_NODE_NAME\" --time-limit=300 --soft-time-limit=240 --max-tasks-per-child=20 --prefetch-multiplier=1 --max-memory-per-child=209715200 --without-gossip --without-mingle --without-heartbeat"
```

### 2. 修改启动脚本 (`appctl.sh`)

添加日志收集器功能，监控所有日志文件并输出到容器日志：

```bash
# 日志收集功能
log_collector() {
    echo "=== 启动日志收集器 ==="
    # 创建日志收集脚本
    cat > "${APP_HOME}/bin/log_collector.sh" << 'EOF'
#!/bin/bash
# 日志收集器：将各个日志文件的内容输出到标准输出
APP_HOME=${APP_HOME:-/root/yiya-ai-bot}
APP_NAME=${APP_NAME:-yiya-ai-bot}

# 监控多个日志文件
tail -f \
    "${APP_HOME}/logs/application.log" \
    "${APP_HOME}/logs/celery.log" \
    "${APP_HOME}/logs/health_monitor.log" \
    "${APP_HOME}/logs/error.log" \
    2>/dev/null | while read line; do
    echo "[$(date '+%Y-%m-%d %H:%M:%S')] $line"
done
EOF
    chmod +x "${APP_HOME}/bin/log_collector.sh"
    
    # 后台运行日志收集器
    nohup "${APP_HOME}/bin/log_collector.sh" > /dev/stdout 2>&1 &
    echo "日志收集器已启动"
}
```

### 3. 修改启动流程

在 `start()` 函数中调用日志收集器：

```bash
# 启动日志收集器
log_collector

# 等待日志收集器启动
sleep 2

# 监控所有日志文件并输出到容器日志
echo "=== 开始监控所有日志 ==="
echo "监控的日志文件:"
echo "  - application.log"
echo "  - celery.log" 
echo "  - health_monitor.log"
echo "  - error.log"
echo ""

# 使用multitail或者简单的tail -f来监控多个日志文件
if command -v multitail >/dev/null 2>&1; then
    # 如果有multitail，使用它来更好地管理多个日志文件
    multitail -e "application" "${APP_HOME}/logs/application.log" \
              -e "celery" "${APP_HOME}/logs/celery.log" \
              -e "health" "${APP_HOME}/logs/health_monitor.log" \
              -e "error" "${APP_HOME}/logs/error.log"
else
    # 使用简单的tail -f监控所有日志文件
    tail -f \
        "${APP_HOME}/logs/application.log" \
        "${APP_HOME}/logs/celery.log" \
        "${APP_HOME}/logs/health_monitor.log" \
        "${APP_HOME}/logs/error.log" \
        2>/dev/null | while read line; do
        echo "[$(date '+%Y-%m-%d %H:%M:%S')] $line"
    done
fi
```

## 验证方法

1. **重新部署容器**：应用新的配置
2. **查看容器日志**：`kubectl logs -f <pod-name>`
3. **检查日志文件**：进入容器查看日志文件是否正确生成
4. **测试Celery任务**：提交一个任务，观察日志输出

## 预期效果

- `kubectl logs` 能够看到所有日志（application、celery、health_monitor、error）
- 日志文件仍然正常保存到容器内的 `/root/yiya-ai-bot/logs/` 目录
- Celery 任务执行日志能够实时显示在容器日志中

## 注意事项

1. **日志轮转**：配置了日志文件大小限制和备份数量
2. **性能影响**：日志收集器会消耗少量资源，但影响很小
3. **兼容性**：支持 multitail 和普通 tail 两种方式
4. **时间戳**：所有日志都会添加时间戳，便于调试 