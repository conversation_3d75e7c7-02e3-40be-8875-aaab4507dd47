# 文档解析性能优化指南

## 已完成的优化

### 1. 日志系统优化
- **移除重复日志配置**：删除了 `docx_utils.py` 中的 `logging.basicConfig` 配置，避免与项目的 loguru 日志系统冲突
- **添加详细性能日志**：为关键函数添加了执行时间监控和详细的步骤日志
- **统一日志记录器**：使用项目统一的 `app_logger` 记录器

### 2. 性能监控
- **函数级性能监控**：为 `get_doc_diff_data_v3`、`parse_icf_docx`、`parse_style_map_from_docx` 等关键函数添加了执行时间统计
- **步骤级性能监控**：细化到每个处理步骤的耗时统计（文件下载、XML解析、差异分析等）
- **文件大小监控**：记录处理文件的大小信息，便于分析性能瓶颈

### 3. 算法优化
- **避免深拷贝**：优化 `process_paragraph_v1` 函数，使用直接文本提取替代 `copy.deepcopy`，减少内存使用和处理时间
- **XML解析器优化**：优先使用 `lxml-xml` 解析器（如果可用），提高XML解析性能
- **错误处理优化**：添加了完善的异常处理和资源清理机制

## 进一步优化建议

### 1. 并发处理优化
```python
# 建议：对于大文档，可以考虑并行处理不同部分
import concurrent.futures
import multiprocessing

def process_document_parts_parallel(docx_path):
    """并行处理文档的不同部分"""
    with concurrent.futures.ThreadPoolExecutor(max_workers=min(4, multiprocessing.cpu_count())) as executor:
        # 并行处理正文、页眉、页脚
        futures = {
            'document': executor.submit(process_document_part, docx_path, 'document'),
            'headers': executor.submit(process_headers_part, docx_path),
            'footers': executor.submit(process_footers_part, docx_path)
        }
        
        results = {}
        for part, future in futures.items():
            results[part] = future.result()
        
        return results
```

### 2. 缓存优化
```python
# 建议：添加样式映射表缓存
from functools import lru_cache
import hashlib

@lru_cache(maxsize=128)
def get_cached_style_map(docx_path, file_hash):
    """缓存样式映射表，避免重复解析"""
    return parse_style_map_from_docx(docx_path)

def get_file_hash(file_path):
    """计算文件哈希值用于缓存"""
    with open(file_path, 'rb') as f:
        return hashlib.md5(f.read()).hexdigest()
```

### 3. 内存优化
```python
# 建议：流式处理大文件
def process_large_document_streaming(docx_path):
    """流式处理大文档，减少内存占用"""
    with ZipFile(docx_path) as doc:
        # 逐个处理XML文件，而不是一次性加载所有内容
        for xml_file in doc.namelist():
            if xml_file.startswith('word/'):
                with doc.open(xml_file) as xml_stream:
                    # 使用iterparse进行流式解析
                    process_xml_stream(xml_stream)
```

### 4. 数据库优化（如果适用）
- 考虑将解析结果缓存到数据库中
- 对于相同文件的重复请求，直接返回缓存结果
- 实现增量更新机制

## 性能基准测试

### 测试环境
- CPU: 建议记录实际测试环境
- 内存: 建议记录实际测试环境
- 存储: SSD/HDD

### 测试结果（示例）
| 文件大小 | 优化前耗时 | 优化后耗时 | 性能提升 |
|---------|-----------|-----------|---------|
| 1MB     | 2.5s      | 1.8s      | 28%     |
| 5MB     | 12.3s     | 8.7s      | 29%     |
| 10MB    | 25.6s     | 17.2s     | 33%     |

## 监控和调试

### 1. 性能日志分析
查看日志文件 `app.log` 中的性能统计信息：
```bash
# 查看处理时间超过10秒的请求
grep "总耗时.*[1-9][0-9]\." app.log

# 查看文件大小分布
grep "文件大小" app.log | awk '{print $NF}' | sort -n
```

### 2. 性能瓶颈识别
- **文件下载慢**：检查网络连接和OSS配置
- **XML解析慢**：考虑文档复杂度，可能需要优化解析算法
- **差异分析慢**：检查修订内容的数量和复杂度

### 3. 内存使用监控
```python
import psutil
import os

def log_memory_usage(step_name):
    """记录内存使用情况"""
    process = psutil.Process(os.getpid())
    memory_info = process.memory_info()
    app_logger.info(f"{step_name} - 内存使用: {memory_info.rss / 1024 / 1024:.2f}MB")
```

## 故障排除

### 常见性能问题
1. **处理时间过长**
   - 检查文件大小和复杂度
   - 查看日志中的步骤耗时分布
   - 考虑是否需要增加服务器资源

2. **内存不足**
   - 监控内存使用情况
   - 考虑实现流式处理
   - 优化数据结构使用

3. **CPU使用率高**
   - 检查是否有死循环或低效算法
   - 考虑使用更高效的XML解析器
   - 实现并发处理

## 配置建议

### 生产环境配置
```python
# gunicorn 配置优化
workers = min(4, multiprocessing.cpu_count())  # 根据CPU核数调整
worker_class = "uvicorn.workers.UvicornWorker"
timeout = 300  # 增加超时时间以处理大文件
max_requests = 500  # 定期重启worker避免内存泄漏
```

### 日志级别配置
```python
# 生产环境建议使用INFO级别
# 开发环境可以使用DEBUG级别查看详细信息
LOG_LEVEL = "INFO"  # 或 "DEBUG"
```
