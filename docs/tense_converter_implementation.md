# 时态转换功能实现说明

## 功能概述

时态转换功能是一个基于 spaCy 和 lemminflect 的英文文本处理服务，能够自动检测英文文本中的将来时态并将其转换为过去时态。该功能主要用于临床试验方案文档的时态标准化处理。

## 核心特性

- **自动时态检测**：智能识别 will/shall + 动词、be going to + 动词等将来时结构
- **多句子支持**：支持输入包含多个句子的长文本，自动分割处理
- **精确转换**：根据主语单复数自动选择 was/were，支持被动语态、进行时态、完成时态
- **位置标记**：返回每个句子的字符位置信息，便于后续处理
- **完整日志**：记录用户输入、处理过程、转换结果和性能指标

## 技术架构

### 依赖库
- **spaCy**: 自然语言处理核心引擎
- **lemminflect**: 英语动词形态变化库
- **FastAPI**: Web 框架
- **Pydantic**: 数据验证和序列化

### 文件结构
```
yiya-ai-bot/
├── models/
│   └── tense_vo.py              # 请求响应数据模型
├── utils/
│   └── tense_utils.py           # 核心转换逻辑
├── api/
│   └── tense_api.py             # API 接口层
└── app.py                       # 主应用入口（已集成）
```

## 详细实现

### 1. 数据模型 (models/tense_vo.py)

```python
class TenseConvertRequest(BaseModel):
    text: str = Field(..., title="输入英文文本", description="需要进行将来时 -> 过去时 转换的文本")

class TenseSentenceResult(BaseModel):
    original_sentence: str = Field(..., title="原句")
    start_char: int = Field(..., title="句子起始字符索引")
    end_char: int = Field(..., title="句子结束字符索引")
    is_future_tense: bool = Field(..., title="是否检测为将来时")
    converted_sentence: str = Field(..., title="转换后的句子")

class TenseConvertResponse(BaseModel):
    code: int = Field(...)
    success: bool = Field(False)
    message: str | None = Field(None)
    result: List[TenseSentenceResult] | Any = Field(None)
    time_cost: int | None = Field(None)
```

### 2. 核心转换逻辑 (utils/tense_utils.py)

#### 主要类：TenseConverter

**初始化**
```python
def __init__(self):
    self.nlp = spacy.load("en_core_web_sm")
```

**核心方法：process_text(text: str) -> list[dict]**
- 使用 spaCy 解析文本，自动分割句子
- 逐句检测将来时结构
- 执行时态转换
- 返回转换结果列表

**支持的将来时结构**

1. **will/shall + 动词**
   - `will be + VBN` → `was/were + VBN` (被动语态)
   - `will be + VBG` → `VBD` (进行时态)
   - `will have + VBN` → `VBD` (完成时态)
   - `will + VERB` → `VBD` (简单将来时)

2. **be going to + 动词**
   - `be going to + VERB` → `VBD`

**智能主语检测**
- 自动识别动词的主语
- 根据主语单复数选择 was/were
- 支持从句和复杂语法结构

### 3. API 接口层 (api/tense_api.py)

**接口信息**
- **路径**: `POST /text/tense/convert`
- **请求体**: `{"text": "英文文本内容"}`
- **响应**: 标准化的成功/失败响应格式

**日志记录**
```python
# 输入日志
app_logger.info(f"收到时态转换请求，输入文本长度: {len(req.text)}")
app_logger.debug(f"输入文本内容: {req.text[:200]}...")

# 处理日志
app_logger.info(f"时态转换完成，总句子数: {total_sentences}, 检测到将来时句子数: {future_tense_sentences}")

# 输出日志
app_logger.info(f"时态转换接口响应成功，耗时: {duration}ms")
```

### 4. 应用集成 (app.py)

```python
from api.tense_api import router as tense_router

# 注册路由
router.include_router(tense_router, include_in_schema=False)
```

## 使用示例

### 1. 单句子输入
```bash
curl -X POST "http://localhost:7860/text/tense/convert" \
     -H "Content-Type: application/json" \
     -d '{"text": "The Drug Accountability Log will be reviewed by the field monitor."}'
```

**响应示例**
```json
{
  "success": true,
  "code": 0,
  "result": [
    {
      "original_sentence": "The Drug Accountability Log will be reviewed by the field monitor.",
      "start_char": 0,
      "end_char": 67,
      "is_future_tense": true,
      "converted_sentence": "The Drug Accountability Log was reviewed by the field monitor."
    }
  ],
  "time_cost": 23
}
```

### 2. 多句子输入
```bash
curl -X POST "http://localhost:7860/text/tense/convert" \
     -H "Content-Type: application/json" \
     -d '{
       "text": "The Drug Accountability Log will be reviewed by the field monitor. A copy of the signed ICF will be given to the patient. Enrolled patients will receive TWO infusions of IP every three weeks."
     }'
```

**响应示例**
```json
{
  "success": true,
  "code": 0,
  "result": [
    {
      "original_sentence": "The Drug Accountability Log will be reviewed by the field monitor.",
      "start_char": 0,
      "end_char": 67,
      "is_future_tense": true,
      "converted_sentence": "The Drug Accountability Log was reviewed by the field monitor."
    },
    {
      "original_sentence": "A copy of the signed ICF will be given to the patient.",
      "start_char": 68,
      "end_char": 125,
      "is_future_tense": true,
      "converted_sentence": "A copy of the signed ICF was given to the patient."
    },
    {
      "original_sentence": "Enrolled patients will receive TWO infusions of IP every three weeks.",
      "start_char": 126,
      "end_char": 185,
      "is_future_tense": true,
      "converted_sentence": "Enrolled patients received TWO infusions of IP every three weeks."
    }
  ],
  "time_cost": 45
}
```

## 安装和部署

### 1. 环境要求
- Python 3.8+
- 已安装项目依赖

### 2. 安装依赖
```bash
# 使用 uv 安装（推荐）
uv add spacy==3.7.5 lemminflect

# 或使用 pip
pip install spacy==3.7.5 lemminflect
```

### 3. 下载语言模型
```bash
# 下载英文语言模型
uv run python -m spacy download en_core_web_sm

# 或使用 pip
python -m spacy download en_core_web_sm
```

### 4. 启动服务
```bash
# 启动主应用
python app.py

# 或使用 gunicorn
gunicorn app:app -w 4 -k uvicorn.workers.UvicornWorker
```

## 性能特点

- **处理速度**: 单句子处理通常在 20-50ms 内完成
- **内存占用**: 使用单例模式，避免重复加载 spaCy 模型
- **并发支持**: 支持多请求并发处理
- **错误处理**: 完善的异常捕获和日志记录

## 扩展和维护

### 1. 添加新的时态结构
在 `utils/tense_utils.py` 的 `process_text` 方法中添加新的检测逻辑：

```python
# 示例：添加 "be about to" 结构支持
elif token.lemma_ == "be" and i + 2 < len(sent) and \
        sent[i + 1].text.lower() == "about" and sent[i + 2].text.lower() == "to":
    # 实现转换逻辑
    pass
```

### 2. 自定义转换规则
修改 `_is_subject_plural_for_verb` 方法以支持特定的语法规则。

### 3. 性能优化
- 考虑使用更大的 spaCy 模型以提高准确性
- 实现缓存机制减少重复计算
- 添加异步处理支持

## 常见问题

### 1. 模型加载失败
**问题**: `OSError: [E050] Can't find model 'en_core_web_sm'`
**解决**: 执行 `python -m spacy download en_core_web_sm`

### 2. 依赖冲突
**问题**: lemminflect 相关错误
**解决**: 确保安装了正确版本的 lemminflect 和 spacy

### 3. 内存不足
**问题**: 处理长文本时内存溢出
**解决**: 考虑分批处理或使用流式处理

## 测试建议

### 1. 单元测试
- 测试各种时态结构的转换
- 测试边界情况和异常输入
- 测试性能指标

### 2. 集成测试
- 测试完整的 API 流程
- 测试并发请求处理
- 测试错误处理和日志记录

### 3. 压力测试
- 测试长文本处理能力
- 测试并发性能
- 测试内存使用情况

## 联系信息

如有问题或需要技术支持，请联系：
- 项目负责人：[姓名]
- 技术文档维护：[姓名]
- 最后更新：2025年1月

---

**注意**: 本功能已完全集成到主应用中，无需额外配置即可使用。
