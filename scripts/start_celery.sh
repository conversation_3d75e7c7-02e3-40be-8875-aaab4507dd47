#!/bin/bash

# 本地启动 Celery Worker 脚本

echo "🚀 启动 Celery Worker..."

# 设置环境变量
export DISABLE_NACOS=true
export NACOS_ENABLE_IN_CELERY=false

# 检查是否在项目根目录
if [ ! -f "celery_task/celery.py" ]; then
    echo "❌ 错误：请在项目根目录下运行此脚本"
    exit 1
fi

# 检查 Redis 连接
echo "🔍 检查 Redis 连接..."
python3 -c "
import redis
from config.settings import settings
try:
    r = redis.Redis(
        host=settings.REDIS_HOST,
        port=settings.REDIS_PORT,
        db=settings.REDIS_DB,
        password=settings.REDIS_PASSWORD,
        socket_connect_timeout=5
    )
    r.ping()
    print('✅ Redis 连接正常')
except Exception as e:
    print(f'❌ Redis 连接失败: {e}')
    exit(1)
"

if [ $? -ne 0 ]; then
    echo "❌ Redis 连接失败，请检查配置"
    exit 1
fi

# 启动 Celery Worker
echo "🎯 启动 Celery Worker..."
celery -A celery_task worker \
    --loglevel=info \
    --pool=prefork \
    --concurrency=4 \
    --time-limit=300 \
    --soft-time-limit=240 \
    --max-tasks-per-child=20 \
    --prefetch-multiplier=1 \
    --without-gossip \
    --without-mingle \
    --without-heartbeat

