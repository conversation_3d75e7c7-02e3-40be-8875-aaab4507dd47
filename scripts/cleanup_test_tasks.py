#!/usr/bin/env python3.11
"""
清理测试任务脚本
用于清理Redis中的测试任务，防止任务恢复机制重复提交
"""

import json
import sys
import os

# 添加项目路径
sys.path.insert(0, '/root/yiya-ai-bot/target/yiya-ai-bot')

from logger.logger import app_logger
from utils.redis_pool import get_redis_client


def cleanup_test_tasks():
    """清理Redis中的测试任务"""
    try:
        # 获取Redis客户端
        redis_client = get_redis_client("cleanup")
        
        # 测试任务的模式
        test_patterns = [
            "protocol/test/",
            "timeout-test-file",
            "non-existent-file",
            "/test/",
            "test-file"
        ]
        
        app_logger.info("开始清理测试任务...")
        
        # 1. 直接删除包含测试模式的键
        deleted_direct = 0
        for pattern in test_patterns:
            keys = redis_client.keys(f"*{pattern}*")
            if keys:
                redis_client.delete(*keys)
                deleted_direct += len(keys)
                app_logger.info(f"删除了 {len(keys)} 个匹配 '{pattern}' 的直接键")
        
        # 2. 检查任务元数据
        task_keys = redis_client.keys("celery-task-meta-*")
        test_task_keys = []
        
        app_logger.info(f"检查 {len(task_keys)} 个任务元数据...")
        
        for key in task_keys:
            try:
                data = redis_client.get(key)
                if not data:
                    continue
                
                # 检查原始数据字符串
                data_str = data if isinstance(data, str) else str(data)
                
                # 检查是否包含测试模式
                is_test_task = any(pattern in data_str for pattern in test_patterns)
                
                if is_test_task:
                    test_task_keys.append(key)
                    app_logger.debug(f"发现测试任务: {key}")
                    
                    # 尝试解析JSON获取更多信息
                    try:
                        task_data = json.loads(data_str)
                        result = task_data.get("result", {})
                        file_key = result.get("file_key", "未知")
                        status = task_data.get("status", "未知")
                        app_logger.info(f"  文件: {file_key}, 状态: {status}")
                    except:
                        app_logger.debug(f"  无法解析任务数据: {data_str[:100]}...")
                        
            except Exception as e:
                app_logger.warning(f"检查任务 {key} 时出错: {e}")
                continue
        
        # 3. 删除测试任务元数据
        deleted_meta = 0
        if test_task_keys:
            redis_client.delete(*test_task_keys)
            deleted_meta = len(test_task_keys)
            app_logger.info(f"删除了 {deleted_meta} 个测试任务元数据")
        
        # 4. 清理可能的队列中的测试任务
        queue_name = "protocol_parse_queue"
        queue_length = redis_client.llen(queue_name)
        deleted_queue = 0
        
        if queue_length > 0:
            app_logger.info(f"检查队列 {queue_name} 中的 {queue_length} 个任务...")
            
            # 获取队列中的所有任务
            tasks = redis_client.lrange(queue_name, 0, -1)
            clean_tasks = []
            
            for task in tasks:
                try:
                    task_str = task if isinstance(task, str) else str(task)
                    is_test_task = any(pattern in task_str for pattern in test_patterns)
                    
                    if is_test_task:
                        deleted_queue += 1
                        app_logger.debug(f"从队列中移除测试任务: {task_str[:100]}...")
                    else:
                        clean_tasks.append(task)
                        
                except Exception as e:
                    app_logger.warning(f"检查队列任务时出错: {e}")
                    clean_tasks.append(task)  # 保留无法检查的任务
            
            # 重建队列（只包含非测试任务）
            if deleted_queue > 0:
                redis_client.delete(queue_name)
                if clean_tasks:
                    redis_client.lpush(queue_name, *clean_tasks)
                app_logger.info(f"从队列中清理了 {deleted_queue} 个测试任务")
        
        total_deleted = deleted_direct + deleted_meta + deleted_queue
        
        app_logger.info(f"✅ 测试任务清理完成:")
        app_logger.info(f"  - 直接键: {deleted_direct} 个")
        app_logger.info(f"  - 任务元数据: {deleted_meta} 个") 
        app_logger.info(f"  - 队列任务: {deleted_queue} 个")
        app_logger.info(f"  - 总计: {total_deleted} 个")
        
        return {
            "deleted_direct": deleted_direct,
            "deleted_meta": deleted_meta,
            "deleted_queue": deleted_queue,
            "total_deleted": total_deleted
        }
        
    except Exception as e:
        app_logger.error(f"清理测试任务失败: {e}")
        return {"error": str(e)}


def main():
    """主函数"""
    app_logger.info("启动测试任务清理脚本...")
    
    result = cleanup_test_tasks()
    
    if "error" in result:
        app_logger.error(f"清理失败: {result['error']}")
        return 1
    else:
        app_logger.info(f"清理成功，共删除 {result['total_deleted']} 个测试相关项目")
        return 0


if __name__ == "__main__":
    exit(main())
