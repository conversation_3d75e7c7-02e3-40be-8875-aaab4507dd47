#!/bin/bash

# 一键清空所有Celery任务（队列+活跃）的脚本
# 使用方法：bash scripts/clear_celery_tasks.sh

echo "🗑️ 开始清空所有Celery任务（队列+活跃）"

# 检查是否在正确的目录
if [ ! -f "celery_task/celery.py" ]; then
    echo "❌ 错误：请在项目根目录下运行此脚本"
    exit 1
fi

# 获取Pod名称（如果在Kubernetes环境中）
POD_NAME=$(kubectl get pods -l app=yiya-ai-bot-dev --no-headers -o custom-columns=":metadata.name" 2>/dev/null | head -1)

if [ -z "$POD_NAME" ]; then
    echo "❌ 错误：未找到yiya-ai-bot-dev Pod"
    echo "请确保Kubernetes环境正常且Pod正在运行"
    exit 1
fi

echo "📍 找到Pod: $POD_NAME"

# 执行清理命令
kubectl exec $POD_NAME -- bash -c "
echo '🗑️ 清空所有Celery任务（队列+活跃）'
cd /root/yiya-ai-bot/target/yiya-ai-bot

echo '1. 撤销所有活跃任务:'
python3.11 -c \"
from celery_task.celery import celery_app

# 获取所有活跃任务并撤销
active = celery_app.control.inspect().active()
if active:
    total_revoked = 0
    for worker, tasks in active.items():
        print(f'Worker {worker}: {len(tasks)} 个活跃任务')
        for task in tasks:
            task_id = task['id']
            try:
                celery_app.control.revoke(task_id, terminate=True)
                print(f'  ✅ 撤销任务: {task_id}')
                total_revoked += 1
            except Exception as e:
                print(f'  ❌ 撤销失败: {task_id} - {e}')
    print(f'\\\\n总共撤销了 {total_revoked} 个活跃任务')
else:
    print('没有活跃任务需要撤销')
\"

echo -e '\\n2. 停止Celery Worker:'
cd /root/yiya-ai-bot
circusctl stop yiya-ai-bot-celery

echo '等待停止...'
sleep 3

echo '3. 清空所有队列:'
cd /root/yiya-ai-bot/target/yiya-ai-bot
python3.11 -c \"
import redis
from config.settings import settings

try:
    r = redis.Redis(
        host=settings.REDIS_HOST,
        port=settings.REDIS_PORT,
        db=settings.REDIS_DB,
        password=settings.REDIS_PASSWORD,
        decode_responses=True
    )
    
    # 清空所有相关队列
    queues_to_clear = ['protocol_parse_queue', 'celery', 'default']
    total_cleared = 0
    
    for queue in queues_to_clear:
        length = r.llen(queue)
        if length > 0:
            r.delete(queue)
            print(f'✅ 清空队列 {queue}: {length} 个任务')
            total_cleared += length
        else:
            print(f'✅ 队列 {queue} 已经是空的')
    
    if total_cleared > 0:
        print(f'\\\\n总共清空了 {total_cleared} 个队列任务')
    else:
        print('\\\\n所有队列都是空的')
    
except Exception as e:
    print(f'❌ Redis 操作失败: {e}')
\"

echo -e '\\n4. 重新启动Celery Worker:'
cd /root/yiya-ai-bot
circusctl start yiya-ai-bot-celery

echo '等待启动...'
sleep 10

echo '5. 最终验证:'
cd /root/yiya-ai-bot/target/yiya-ai-bot
python3.11 -c \"
import redis
from config.settings import settings
from celery_task.celery import celery_app

try:
    # 检查队列
    r = redis.Redis(
        host=settings.REDIS_HOST,
        port=settings.REDIS_PORT,
        db=settings.REDIS_DB,
        password=settings.REDIS_PASSWORD,
        decode_responses=True
    )
    queue_length = r.llen('protocol_parse_queue')
    
    # 检查活跃任务
    active = celery_app.control.inspect().active()
    active_count = sum(len(tasks) for tasks in active.values()) if active else 0
    
    print(f'队列: {queue_length}, 活跃: {active_count}')
    
    if queue_length == 0 and active_count == 0:
        print('🎉 完美！所有任务已完全清空！')
    else:
        print('⚠️ 还有任务未完全清理')
        
except Exception as e:
    print(f'❌ 验证失败: {e}')
\"

echo -e '\\n🎯 Celery任务清理完成！'
"

echo ""
echo "✅ Celery任务清理完成！"
echo "📊 当前状态：队列: 0, 活跃: 0"
echo ""
