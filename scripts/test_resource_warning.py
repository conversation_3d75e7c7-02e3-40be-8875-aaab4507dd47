#!/usr/bin/env python3
"""
测试资源警告修复的脚本
"""

import asyncio
import warnings
import sys
import os

# 添加项目根目录到路径
sys.path.insert(0, os.path.dirname(os.path.dirname(os.path.abspath(__file__))))

from utils.resource_warning_handler import ResourceWarningHandler, setup_resource_warning_handling
from utils.resource_manager import AsyncResourceManager, cleanup_async_resources
from logger.logger import app_logger


async def test_async_resource_cleanup():
    """测试异步资源清理"""
    app_logger.info("开始测试异步资源清理...")
    
    # 创建一些模拟的异步资源
    async def create_mock_stream():
        await asyncio.sleep(0.1)
        return {"type": "mock_stream", "data": "test"}
    
    # 模拟资源创建
    resources = []
    for i in range(5):
        resource = await create_mock_stream()
        resources.append(resource)
    
    app_logger.info(f"创建了 {len(resources)} 个模拟资源")
    
    # 清理资源
    await cleanup_async_resources()
    
    app_logger.info("异步资源清理测试完成")


def test_stream_wrapper():
    """测试流包装器"""
    app_logger.info("开始测试流包装器...")
    
    from utils.resource_manager import StreamWrapper, wrap_stream
    
    # 模拟一个流对象
    class MockStream:
        def __init__(self):
            self.closed = False
        
        def read(self, size=None):
            return b"test data"
        
        def close(self):
            self.closed = True
    
    # 测试 StreamWrapper
    mock_stream = MockStream()
    wrapped_stream = StreamWrapper(mock_stream)
    
    # 读取数据
    data = wrapped_stream.read()
    app_logger.info(f"读取数据: {data}")
    
    # 关闭流
    wrapped_stream.close()
    app_logger.info(f"流已关闭: {mock_stream.closed}")
    
    # 测试 wrap_stream 函数
    mock_stream2 = MockStream()
    wrapped_stream2 = wrap_stream(mock_stream2)
    
    # 使用上下文管理器
    with wrapped_stream2 as stream:
        data = stream.read()
        app_logger.info(f"使用上下文管理器读取数据: {data}")
    
    app_logger.info(f"上下文管理器退出后流状态: {mock_stream2.closed}")
    app_logger.info("流包装器测试完成")


def test_resource_warning_handler():
    """测试资源警告处理器"""
    app_logger.info("开始测试资源警告处理器...")
    
    with ResourceWarningHandler(enable_tracemalloc=False):
        # 触发一个 ResourceWarning
        import tempfile
        import io
        
        # 创建一个临时文件流
        temp_file = tempfile.NamedTemporaryFile(delete=False)
        temp_file.write(b"test data")
        temp_file.close()
        
        # 故意不关闭流来触发警告
        with open(temp_file.name, 'rb') as f:
            stream = io.BytesIO(f.read())
            # 不调用 stream.close() 来模拟资源泄漏
        
        # 清理临时文件
        os.unlink(temp_file.name)
        
        # 强制垃圾回收来触发警告
        import gc
        gc.collect()
    
    app_logger.info("资源警告处理器测试完成")


async def main():
    """主测试函数"""
    app_logger.info("开始资源警告修复测试...")
    
    # 设置资源警告处理
    setup_resource_warning_handling()
    
    # 运行各种测试
    test_stream_wrapper()
    test_resource_warning_handler()
    await test_async_resource_cleanup()
    
    app_logger.info("所有测试完成")


if __name__ == "__main__":
    asyncio.run(main()) 