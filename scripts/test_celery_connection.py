#!/usr/bin/env python3
"""
测试 Celery 连接和任务提交的脚本
"""

import os
import sys
import time

# 添加项目根目录到 Python 路径
sys.path.insert(0, os.path.dirname(os.path.dirname(os.path.abspath(__file__))))

from celery_task.celery import celery_app
from celery_task.protocol_task import extract_protocol_task
from logger.logger import app_logger


def test_celery_connection():
    """测试 Celery 连接"""
    try:
        # 检查 broker 连接
        app_logger.info("测试 Celery broker 连接...")
        inspect = celery_app.control.inspect()
        stats = inspect.stats()
        
        if stats:
            app_logger.info("✅ Celery broker 连接成功")
            app_logger.info(f"活跃的 workers: {list(stats.keys())}")
        else:
            app_logger.warning("⚠️ 没有活跃的 workers")
            
        return True
    except Exception as e:
        app_logger.error(f"❌ Celery broker 连接失败: {e}")
        return False


def test_task_submission():
    """测试任务提交"""
    try:
        app_logger.info("测试任务提交...")
        
        # 提交一个测试任务
        task = extract_protocol_task.delay("test/protocol.docx", False)
        task_id = task.id
        
        app_logger.info(f"✅ 任务提交成功，Task ID: {task_id}")
        
        # 等待几秒检查任务状态
        time.sleep(3)
        
        # 检查任务状态
        result = task.result
        state = task.state
        
        app_logger.info(f"任务状态: {state}")
        if result:
            app_logger.info(f"任务结果: {result}")
        
        return True
    except Exception as e:
        app_logger.error(f"❌ 任务提交失败: {e}")
        return False


def main():
    """主函数"""
    app_logger.info("开始 Celery 连接测试...")
    
    # 测试连接
    if not test_celery_connection():
        app_logger.error("Celery 连接测试失败")
        return 1
    
    # 测试任务提交
    if not test_task_submission():
        app_logger.error("任务提交测试失败")
        return 1
    
    app_logger.info("✅ 所有测试通过")
    return 0


if __name__ == "__main__":
    exit(main()) 