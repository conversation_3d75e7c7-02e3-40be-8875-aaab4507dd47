#!/usr/bin/env python3
"""
Celery 诊断脚本
"""

import os
import sys
import redis
import json

# 添加项目根目录到 Python 路径
sys.path.insert(0, os.path.dirname(os.path.dirname(os.path.abspath(__file__))))

from config.settings import settings
from celery_task.celery import celery_app
from logger.logger import app_logger


def check_redis_connection():
    """检查 Redis 连接"""
    try:
        app_logger.info("检查 Redis 连接...")
        
        # 检查 broker Redis
        broker_r = redis.Redis(
            host=settings.REDIS_HOST,
            port=settings.REDIS_PORT,
            db=settings.REDIS_DB,
            password=settings.REDIS_PASSWORD
        )
        broker_r.ping()
        app_logger.info("✅ Broker Redis 连接正常")
        
        # 检查 result backend Redis
        result_r = redis.Redis(
            host=settings.REDIS_HOST,
            port=settings.REDIS_PORT,
            db=settings.REDIS_TASK_DB,
            password=settings.REDIS_PASSWORD
        )
        result_r.ping()
        app_logger.info("✅ Result Backend Redis 连接正常")
        
        return True
    except Exception as e:
        app_logger.error(f"❌ Redis 连接失败: {e}")
        return False


def check_celery_workers():
    """检查 Celery Workers"""
    try:
        app_logger.info("检查 Celery Workers...")
        
        inspect = celery_app.control.inspect()
        
        # 检查活跃的 workers
        active_workers = inspect.active()
        if active_workers:
            app_logger.info("✅ 有活跃的 workers:")
            for worker_name, tasks in active_workers.items():
                app_logger.info(f"  - {worker_name}: {len(tasks)} 个活跃任务")
        else:
            app_logger.warning("⚠️ 没有活跃的 workers")
        
        # 检查注册的 workers
        registered_workers = inspect.registered()
        if registered_workers:
            app_logger.info("✅ 注册的 workers:")
            for worker_name, tasks in registered_workers.items():
                app_logger.info(f"  - {worker_name}: {len(tasks)} 个注册任务")
        else:
            app_logger.warning("⚠️ 没有注册的 workers")
        
        # 检查 stats
        stats = inspect.stats()
        if stats:
            app_logger.info("✅ Worker 统计信息:")
            for worker_name, stat in stats.items():
                app_logger.info(f"  - {worker_name}: {stat.get('pool', {}).get('processes', 'N/A')} 个进程")
        
        return True
    except Exception as e:
        app_logger.error(f"❌ 检查 Workers 失败: {e}")
        return False


def check_celery_queues():
    """检查 Celery 队列"""
    try:
        app_logger.info("检查 Celery 队列...")
        
        # 检查队列中的任务
        inspect = celery_app.control.inspect()
        reserved_tasks = inspect.reserved()
        
        if reserved_tasks:
            app_logger.info("✅ 队列中的任务:")
            for worker_name, tasks in reserved_tasks.items():
                app_logger.info(f"  - {worker_name}: {len(tasks)} 个待处理任务")
        else:
            app_logger.info("ℹ️ 队列中没有待处理任务")
        
        return True
    except Exception as e:
        app_logger.error(f"❌ 检查队列失败: {e}")
        return False


def check_celery_config():
    """检查 Celery 配置"""
    try:
        app_logger.info("检查 Celery 配置...")
        
        config = celery_app.conf
        
        app_logger.info(f"Broker URL: {config.broker_url}")
        app_logger.info(f"Result Backend: {config.result_backend}")
        app_logger.info(f"Task Serializer: {config.task_serializer}")
        app_logger.info(f"Result Serializer: {config.result_serializer}")
        app_logger.info(f"Accept Content: {config.accept_content}")
        app_logger.info(f"Default Queue: {config.task_default_queue}")
        
        return True
    except Exception as e:
        app_logger.error(f"❌ 检查配置失败: {e}")
        return False


def main():
    """主函数"""
    app_logger.info("开始 Celery 诊断...")
    
    checks = [
        ("Redis 连接", check_redis_connection),
        ("Celery 配置", check_celery_config),
        ("Celery Workers", check_celery_workers),
        ("Celery 队列", check_celery_queues),
    ]
    
    results = []
    for name, check_func in checks:
        app_logger.info(f"\n=== 检查 {name} ===")
        try:
            result = check_func()
            results.append((name, result))
        except Exception as e:
            app_logger.error(f"检查 {name} 时发生异常: {e}")
            results.append((name, False))
    
    # 总结
    app_logger.info("\n=== 诊断总结 ===")
    all_passed = True
    for name, result in results:
        status = "✅ 通过" if result else "❌ 失败"
        app_logger.info(f"{name}: {status}")
        if not result:
            all_passed = False
    
    if all_passed:
        app_logger.info("🎉 所有检查都通过了！")
    else:
        app_logger.error("⚠️ 部分检查失败，请查看上面的详细信息")
    
    return 0 if all_passed else 1


if __name__ == "__main__":
    exit(main()) 