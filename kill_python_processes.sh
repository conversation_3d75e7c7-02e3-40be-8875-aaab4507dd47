#!/bin/bash
# 获取所有Python多进程相关的PID
PIDS=$(ps aux | grep -E "(python.*multiprocessing|python.*spawn_main)" | grep -v grep | awk '{print $2}')

if [ -z "$PIDS" ]; then
    echo "没有找到Python多进程"
    exit 0
fi

echo "找到以下Python进程:"
ps aux | grep -E "(python.*multiprocessing|python.*spawn_main)" | grep -v grep

echo "正在杀死进程..."
for pid in $PIDS; do
    if kill -9 $pid 2>/dev/null; then
        echo "已杀死进程 $pid"
    else
        echo "无法杀死进程 $pid (可能已结束或无权限)"
    fi
done

echo "清理完成"
