#### aiohappyeyeballs==2.4.3
aiohttp==3.11.7
#### aiosignal==1.3.1
#### annotated-types==0.7.0
#### anyio==4.6.2.post1
attrs==24.2.0
beautifulsoup4==4.12.3
#### blinker==1.9.0
certifi==2024.8.30
charset-normalizer==3.4.0
click==8.1.7
#### dataclasses-json==0.6.7
#### Deprecated==1.2.15
#### dirtyjson==1.0.8
#### distro==1.9.0
fastapi==0.115.5
filetype==1.2.0
Flask==3.1.0
#### frozenlist==1.5.0
#### fsspec==2024.9.0
#### greenlet==3.1.1
gunicorn==23.0.0
#### h11==0.14.0
httpcore==1.0.7
httpx==0.27.2
idna==3.10
#### itsdangerous==2.2.0
#### Jinja2==3.1.4
#### jiter==0.7.1
#### joblib==1.4.2
loguru==0.7.2
#### markupsafe==2.1.1
#### marshmallow==3.23.1
#### multidict==6.1.0
#### mypy-extensions==1.0.0
nacos-sdk-python==2.0.9
#### networkx==3.4.2
#### nltk==3.9.1
numpy==1.26.4
#### openai==1.55.0
#### packaging==24.2
pandas==2.2.3
#### pillow==11.0.0
#### propcache==0.2.0
pydantic==2.10.4
pydantic_core==2.27.2
pydantic-settings==2.10.1
pypdf==5.1.0
#### python-dateutil==2.9.0.post0
python-multipart==0.0.19
#### pytz==2024.2
#### PyYAML==6.0.2
regex==2024.11.6
requests==2.32.3
#### six==1.16.0
#### sniffio==1.3.1
#### soupsieve==2.6
#### SQLAlchemy==2.0.36
starlette==0.41.3
striprtf==0.0.26
#### tenacity==8.5.0
#### tiktoken==0.8.0
#### tqdm==4.67.0
#### typing-inspect==0.9.0
#### typing_extensions==4.12.
urllib3==2.5.0
uvicorn==0.32.1
Werkzeug==3.1.3
#### wrapt==1.17.0
yarl==1.18.0
nest-asyncio==1.6.0
oss2==2.19.1
# paddlehub==2.4.0
#### shapely==2.0.6
#### pyclipper==1.3.0.post6
# paddleocr==2.9.1
#### dashscope==1.20.14
# paddlepaddle-gpu==2.6.2
torch==2.8.0
#### torchaudio==2.2.2
#### funasr==1.2.0
# onnxconverter-common==1.14.0
#### pydub==0.25.1
unoconv==0.9.0
circus
opencv-python-headless
#### markdown-it-py==3.0.0
#### markdownify==0.14.1
markitdown==0.0.1a3
python-docx
PyMuPDF
patool
pdf2image==1.17.0
orjson==3.10.18
pymysql==1.1.1
celery==5.5.3
redis==6.2.0
rank-bm25==0.2.2
docxtpl==0.19.1
pyreadstat==1.2.9
llama-parse==0.5.15
jieba
lemminflect==0.2.3
spacy==3.8.7
# spacy依赖的en_core_web_sm模型
en_core_web_sm @ https://yiya-dev.oss-cn-hangzhou.aliyuncs.com/devops/en_core_web_sm-3.8.0-py3-none-any.whl

## 下面2个包只有生产环境需要，本地不需要打开注释
#opentelemetry-distro
#opentelemetry-exporter-otlp
