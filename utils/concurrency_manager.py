#!/usr/bin/env python3.11
"""
并发控制管理器
提供资源访问控制、任务队列管理、并发限制等功能
"""

import threading
import time
from collections import defaultdict, deque
from contextlib import contextmanager
from typing import Any, Dict

from logger.logger import app_logger
from utils.distributed_lock import distributed_lock
from utils.redis_pool import get_redis_client


class ConcurrencyManager:
    """并发控制管理器"""

    def __init__(self):
        self._local_locks = {}  # 本地锁
        self._resource_counters = defaultdict(int)  # 资源计数器
        self._resource_limits = {}  # 资源限制
        self._active_tasks = defaultdict(set)  # 活跃任务
        self._task_queues = defaultdict(deque)  # 任务队列
        self._lock = threading.RLock()
        self.redis_client = get_redis_client(pool_name="concurrency_control")

    def set_resource_limit(self, resource_name: str, limit: int):
        """
        设置资源并发限制

        Args:
            resource_name: 资源名称
            limit: 并发限制数量
        """
        with self._lock:
            self._resource_limits[resource_name] = limit
            app_logger.info(f"设置资源并发限制: {resource_name} = {limit}")

    @contextmanager
    def acquire_resource(self, resource_name: str, task_id: str = None, timeout: int = 30):
        """
        获取资源访问权限

        Args:
            resource_name: 资源名称
            task_id: 任务ID
            timeout: 超时时间
        """
        acquired = False
        start_time = time.time()

        try:
            # 检查本地并发限制
            while True:
                with self._lock:
                    current_count = self._resource_counters[resource_name]
                    limit = self._resource_limits.get(resource_name, float("inf"))

                    if current_count < limit:
                        self._resource_counters[resource_name] += 1
                        if task_id:
                            self._active_tasks[resource_name].add(task_id)
                        acquired = True
                        break

                # 检查超时
                if time.time() - start_time > timeout:
                    raise TimeoutError(f"获取资源超时: {resource_name}")

                time.sleep(0.1)  # 短暂等待

            app_logger.debug(f"获取资源成功: {resource_name} (当前: {current_count + 1}/{limit})")
            yield

        finally:
            if acquired:
                with self._lock:
                    self._resource_counters[resource_name] -= 1
                    if task_id:
                        self._active_tasks[resource_name].discard(task_id)
                app_logger.debug(f"释放资源: {resource_name}")

    @contextmanager
    def file_access_lock(self, file_key: str, mode: str = "read", timeout: int = 30):
        """
        文件访问锁

        Args:
            file_key: 文件键
            mode: 访问模式 ('read', 'write')
            timeout: 超时时间
        """
        lock_key = f"file_access:{file_key}:{mode}"

        with distributed_lock(lock_key, timeout=timeout):
            yield

    @contextmanager
    def task_execution_lock(self, task_type: str, max_concurrent: int = 5):
        """
        任务执行并发控制

        Args:
            task_type: 任务类型
            max_concurrent: 最大并发数
        """
        self.set_resource_limit(f"task_{task_type}", max_concurrent)

        with self.acquire_resource(f"task_{task_type}"):
            yield

    def get_resource_stats(self) -> Dict[str, Any]:
        """
        获取资源使用统计

        Returns:
            Dict: 资源统计信息
        """
        with self._lock:
            stats = {}
            for resource_name, current_count in self._resource_counters.items():
                limit = self._resource_limits.get(resource_name, float("inf"))
                active_tasks = list(self._active_tasks[resource_name])

                stats[resource_name] = {
                    "current_count": current_count,
                    "limit": limit if limit != float("inf") else None,
                    "usage_ratio": current_count / limit if limit != float("inf") else 0,
                    "active_tasks": active_tasks,
                    "queue_size": len(self._task_queues[resource_name]),
                }

            return stats

    def check_deadlock_risk(self) -> Dict[str, Any]:
        """
        检查死锁风险

        Returns:
            Dict: 死锁风险分析
        """
        with self._lock:
            risk_analysis = {"high_usage_resources": [], "potential_bottlenecks": [], "recommendations": []}

            for resource_name, current_count in self._resource_counters.items():
                limit = self._resource_limits.get(resource_name, float("inf"))

                if limit != float("inf"):
                    usage_ratio = current_count / limit

                    if usage_ratio > 0.9:  # 90% 以上使用率
                        risk_analysis["high_usage_resources"].append(
                            {
                                "resource": resource_name,
                                "usage_ratio": usage_ratio,
                                "current": current_count,
                                "limit": limit,
                            }
                        )

                    if usage_ratio > 0.8 and len(self._task_queues[resource_name]) > 0:
                        risk_analysis["potential_bottlenecks"].append(
                            {
                                "resource": resource_name,
                                "queue_size": len(self._task_queues[resource_name]),
                                "usage_ratio": usage_ratio,
                            }
                        )

            # 生成建议
            if risk_analysis["high_usage_resources"]:
                risk_analysis["recommendations"].append("考虑增加高使用率资源的并发限制")

            if risk_analysis["potential_bottlenecks"]:
                risk_analysis["recommendations"].append("存在潜在瓶颈，建议优化任务调度")

            return risk_analysis


class TaskThrottler:
    """任务限流器"""

    def __init__(self, redis_pool_name: str = "task_throttler"):
        self.redis_client = get_redis_client(pool_name=redis_pool_name)

    def is_allowed(self, key: str, limit: int, window_seconds: int = 60) -> bool:
        """
        检查是否允许执行（滑动窗口限流）

        Args:
            key: 限流键
            limit: 限制数量
            window_seconds: 时间窗口（秒）

        Returns:
            bool: 是否允许
        """
        now = time.time()
        pipeline = self.redis_client.pipeline()

        # 清理过期记录
        pipeline.zremrangebyscore(key, 0, now - window_seconds)

        # 获取当前窗口内的计数
        pipeline.zcard(key)

        # 添加当前请求
        pipeline.zadd(key, {str(now): now})

        # 设置过期时间
        pipeline.expire(key, window_seconds)

        results = pipeline.execute()
        current_count = results[1]

        if current_count < limit:
            app_logger.debug(f"限流检查通过: {key} ({current_count}/{limit})")
            return True
        else:
            # 如果超过限制，移除刚添加的记录
            self.redis_client.zrem(key, str(now))
            app_logger.warning(f"限流检查失败: {key} ({current_count}/{limit})")
            return False

    @contextmanager
    def throttle(self, key: str, limit: int, window_seconds: int = 60):
        """
        限流上下文管理器

        Args:
            key: 限流键
            limit: 限制数量
            window_seconds: 时间窗口（秒）
        """
        if not self.is_allowed(key, limit, window_seconds):
            if "file_processing:" in key:
                file_name = key.split(":")[-1]
                raise RuntimeError(f"文件正在处理中，请稍后再试: {file_name}")
            else:
                raise RuntimeError(f"请求被限流: {key}")

        try:
            yield
        except Exception:
            # 如果执行失败，可以考虑回滚计数
            pass


# 全局并发控制管理器
concurrency_manager = ConcurrencyManager()
task_throttler = TaskThrottler()


@contextmanager
def resource_limit(resource_name: str, limit: int = 5, task_id: str = None):
    """
    便捷函数：资源并发限制

    Args:
        resource_name: 资源名称
        limit: 并发限制
        task_id: 任务ID
    """
    concurrency_manager.set_resource_limit(resource_name, limit)
    with concurrency_manager.acquire_resource(resource_name, task_id):
        yield


@contextmanager
def file_lock(file_key: str, mode: str = "read"):
    """便捷函数：文件访问锁"""
    with concurrency_manager.file_access_lock(file_key, mode):
        yield


@contextmanager
def task_limit(task_type: str, max_concurrent: int = 5):
    """便捷函数：任务并发限制"""
    with concurrency_manager.task_execution_lock(task_type, max_concurrent):
        yield


@contextmanager
def rate_limit(key: str, limit: int, window_seconds: int = 60):
    """便捷函数：速率限制"""
    with task_throttler.throttle(key, limit, window_seconds):
        yield
