#!/usr/bin/env python3.11
"""
分布式锁管理器
基于 Redis 实现分布式锁，防止并发操作冲突
"""

import time
import uuid
import threading
from typing import Optional, Any
from contextlib import contextmanager

from logger.logger import app_logger
from utils.redis_pool import get_redis_client


class DistributedLock:
    """Redis 分布式锁"""
    
    def __init__(self, 
                 lock_key: str,
                 timeout: int = 30,
                 retry_interval: float = 0.1,
                 max_retries: int = 100,
                 redis_pool_name: str = "distributed_lock"):
        """
        初始化分布式锁
        
        Args:
            lock_key: 锁的键名
            timeout: 锁超时时间（秒）
            retry_interval: 重试间隔（秒）
            max_retries: 最大重试次数
            redis_pool_name: Redis 连接池名称
        """
        self.lock_key = f"lock:{lock_key}"
        self.timeout = timeout
        self.retry_interval = retry_interval
        self.max_retries = max_retries
        self.redis_client = get_redis_client(pool_name=redis_pool_name)
        self.lock_value = None
        self._local_lock = threading.RLock()
    
    def acquire(self, blocking: bool = True) -> bool:
        """
        获取锁
        
        Args:
            blocking: 是否阻塞等待
            
        Returns:
            bool: 是否成功获取锁
        """
        with self._local_lock:
            if self.lock_value is not None:
                # 已经持有锁
                return True
            
            # 生成唯一的锁值
            lock_value = str(uuid.uuid4())
            
            if blocking:
                # 阻塞模式：重试直到获取锁或超时
                for attempt in range(self.max_retries):
                    try:
                        # 使用 SET NX EX 原子操作
                        if self.redis_client.set(self.lock_key, lock_value, nx=True, ex=self.timeout):
                            self.lock_value = lock_value
                            app_logger.debug(f"成功获取分布式锁: {self.lock_key}")
                            return True
                    except Exception as e:
                        app_logger.warning(f"获取分布式锁失败 {self.lock_key}: {e}")
                    
                    if attempt < self.max_retries - 1:
                        time.sleep(self.retry_interval)
                
                app_logger.warning(f"获取分布式锁超时: {self.lock_key}")
                return False
            else:
                # 非阻塞模式：尝试一次
                try:
                    if self.redis_client.set(self.lock_key, lock_value, nx=True, ex=self.timeout):
                        self.lock_value = lock_value
                        app_logger.debug(f"成功获取分布式锁: {self.lock_key}")
                        return True
                except Exception as e:
                    app_logger.warning(f"获取分布式锁失败 {self.lock_key}: {e}")
                
                return False
    
    def release(self) -> bool:
        """
        释放锁
        
        Returns:
            bool: 是否成功释放锁
        """
        with self._local_lock:
            if self.lock_value is None:
                # 没有持有锁
                return True
            
            try:
                # 使用 Lua 脚本确保原子性：只有持有锁的客户端才能释放锁
                lua_script = """
                if redis.call("get", KEYS[1]) == ARGV[1] then
                    return redis.call("del", KEYS[1])
                else
                    return 0
                end
                """
                
                result = self.redis_client.eval(lua_script, 1, self.lock_key, self.lock_value)
                
                if result == 1:
                    app_logger.debug(f"成功释放分布式锁: {self.lock_key}")
                    self.lock_value = None
                    return True
                else:
                    app_logger.warning(f"释放分布式锁失败，锁可能已过期: {self.lock_key}")
                    self.lock_value = None
                    return False
                    
            except Exception as e:
                app_logger.error(f"释放分布式锁异常 {self.lock_key}: {e}")
                self.lock_value = None
                return False
    
    def extend(self, additional_time: int = 30) -> bool:
        """
        延长锁的过期时间
        
        Args:
            additional_time: 延长的时间（秒）
            
        Returns:
            bool: 是否成功延长
        """
        with self._local_lock:
            if self.lock_value is None:
                return False
            
            try:
                # 使用 Lua 脚本确保原子性
                lua_script = """
                if redis.call("get", KEYS[1]) == ARGV[1] then
                    return redis.call("expire", KEYS[1], ARGV[2])
                else
                    return 0
                end
                """
                
                result = self.redis_client.eval(lua_script, 1, self.lock_key, self.lock_value, additional_time)
                
                if result == 1:
                    app_logger.debug(f"成功延长分布式锁: {self.lock_key}")
                    return True
                else:
                    app_logger.warning(f"延长分布式锁失败: {self.lock_key}")
                    return False
                    
            except Exception as e:
                app_logger.error(f"延长分布式锁异常 {self.lock_key}: {e}")
                return False
    
    def is_locked(self) -> bool:
        """
        检查是否持有锁
        
        Returns:
            bool: 是否持有锁
        """
        return self.lock_value is not None
    
    def __enter__(self):
        """上下文管理器入口"""
        if not self.acquire():
            raise RuntimeError(f"无法获取分布式锁: {self.lock_key}")
        return self
    
    def __exit__(self, exc_type, exc_val, exc_tb):
        """上下文管理器出口"""
        self.release()


@contextmanager
def distributed_lock(lock_key: str, 
                    timeout: int = 30,
                    retry_interval: float = 0.1,
                    max_retries: int = 100,
                    redis_pool_name: str = "distributed_lock"):
    """
    分布式锁上下文管理器
    
    Args:
        lock_key: 锁的键名
        timeout: 锁超时时间（秒）
        retry_interval: 重试间隔（秒）
        max_retries: 最大重试次数
        redis_pool_name: Redis 连接池名称
        
    Usage:
        with distributed_lock("task_status_update"):
            # 临界区代码
            update_task_status()
    """
    lock = DistributedLock(
        lock_key=lock_key,
        timeout=timeout,
        retry_interval=retry_interval,
        max_retries=max_retries,
        redis_pool_name=redis_pool_name
    )
    
    try:
        if not lock.acquire():
            raise RuntimeError(f"无法获取分布式锁: {lock_key}")
        yield lock
    finally:
        lock.release()


class TaskStateLock:
    """任务状态更新专用锁"""
    
    @staticmethod
    @contextmanager
    def lock_task_state(task_id: str, timeout: int = 10):
        """
        任务状态更新锁
        
        Args:
            task_id: 任务ID
            timeout: 锁超时时间
            
        Usage:
            with TaskStateLock.lock_task_state(task_id):
                # 更新任务状态
                task.update_state(...)
        """
        lock_key = f"task_state:{task_id}"
        with distributed_lock(lock_key, timeout=timeout, max_retries=50):
            yield
