import spacy
import lemminflect  # noqa: F401  确保注册 Token._.inflect 扩展
from spacy.tokens import Token


class TenseConverter:
    def __init__(self):
        # 初始化spaCy模型
        self.nlp = spacy.load("en_core_web_sm")

    def _is_subject_plural_for_verb(self, verb_token: Token) -> bool:
        """
        为给定的动词token查找其主语并判断是否为复数。
        查找 nsubj (主动) 或 nsubjpass (被动)。
        简化并尝试使其更稳健。
        """
        # --- 策略：直接在 verb_token 的子节点中查找主语 ---
        for child in verb_token.children:
            if child.dep_ == "nsubj":
                return self._is_token_plural(child)
            elif child.dep_ == "nsubjpass":
                return self._is_token_plural(child)

        # --- 策略：如果动词在从句中 (relcl, acl)，主语可能是被修饰的名词 ---
        if verb_token.dep_ in ("relcl", "acl"):
            modified_noun = verb_token.head
            if modified_noun:
                return self._is_token_plural(modified_noun)

        # --- 策略：向上游走一层，查找主句动词的主语 (有限尝试) ---
        if verb_token.dep_ in ("xcomp", "ccomp") and verb_token.head != verb_token:  # Avoid ROOT
            ancestor_verb = verb_token.head
            for child in ancestor_verb.children:
                if child.dep_ == "nsubj":
                    return self._is_token_plural(child)
                elif child.dep_ == "nsubjpass":
                    return self._is_token_plural(child)

        # 如果以上方法都找不到，返回默认值
        return False  # 默认单数

    def _is_token_plural(self, token: Token) -> bool:
        """判断一个token（通常是名词或代词）是否为复数。"""
        # 代词
        if token.text.lower() in ('i', 'he', 'she', 'it') or token.lemma_.lower() in ('this', 'that'):
            return False
        if token.text.lower() in ('you', 'we', 'they') or token.lemma_.lower() in ('these', 'those'):
            return True
        # 名词
        if token.pos_ == "NOUN":
            # 使用 spaCy 的 morphological 信息 (最可靠)
            if hasattr(token, 'morph'):
                morph_dict = token.morph.to_dict()
                if morph_dict.get("Number") == "Plur":
                    return True
                elif morph_dict.get("Number") == "Sing":
                    return False
            # 启发式方法 - 检查限定词
            for child in token.children:
                if child.dep_ == "det" and child.text.lower() in ('these', 'those'):
                    return True
                if child.dep_ == "det" and child.text.lower() in ('this', 'that'):
                    return False
            return False  # 默认单数
        return False  # 默认单数

    def process_text(self, text: str) -> dict:
        """
        处理输入文本，执行时态检测和转换。
        整段输入，整段输出。
        """
        doc = self.nlp(text)
        original_text = text
        new_tokens = [token.text_with_ws for token in doc]
        tokens_modified = False
        original_chars = []
        target_chars = []
        is_future = False

        i = 0
        while i < len(doc):
            token = doc[i]

            # --- 检测并转换 "will/shall + ..." 结构 ---
            if token.lemma_ in ["will", "shall"] and token.pos_ == "AUX":
                is_future = True

                # 查找 will/shall 后面的第一个助动词或动词 (跳过副词等)
                j = i + 1
                while j < len(doc) and doc[j].pos_ not in ("VERB", "AUX"):
                    j += 1

                if j < len(doc):
                    next_token = doc[j]

                    # --- 优化后的逻辑：处理 "will be ..." ---
                    if next_token.lemma_ == "be":
                        k = j + 1
                        while k < len(doc) and doc[k].pos_ not in ("VERB", "AUX"):
                            k += 1

                        if k < len(doc):
                            main_verb_token = doc[k]
                            # 情况 1.1: 被动语态 "will be VBN"
                            if main_verb_token.tag_ == "VBN":
                                is_plural = self._is_subject_plural_for_verb(main_verb_token)
                                original_chars.extend([
                                    [token.idx, token.idx + len(token.text)],
                                    [next_token.idx, next_token.idx + len(next_token.text)]
                                ])
                                new_tokens[i] = ("were " if is_plural else "was ")
                                new_tokens[j] = ""  # 移除 'be'
                                target_chars.append([
                                    token.idx, 
                                    token.idx + len("were" if is_plural else "was")
                                ])
                                tokens_modified = True
                                i = k + 1
                                continue
                            # 情况 1.2: 进行时态 "will be VBG"
                            elif main_verb_token.tag_ == "VBG":
                                past_form = main_verb_token._.inflect("VBD")
                                if past_form:
                                    original_chars.extend([
                                        [token.idx, token.idx + len(token.text)],
                                        [next_token.idx, next_token.idx + len(next_token.text)],
                                        [main_verb_token.idx, main_verb_token.idx + len(main_verb_token.text)]
                                    ])
                                    new_tokens[i] = ""  # 移除 'will'
                                    new_tokens[j] = ""  # 移除 'be'
                                    new_tokens[k] = past_form + main_verb_token.whitespace_
                                    target_chars.append([
                                        main_verb_token.idx, 
                                        main_verb_token.idx + len(past_form)
                                    ])
                                    tokens_modified = True
                                i = k + 1
                                continue
                            else:
                                # 其他带有动词的情况，当作一般将来时处理
                                is_plural = self._is_subject_plural_for_verb(token)
                                original_chars.extend([
                                    [token.idx, token.idx + len(token.text)],
                                    [next_token.idx, next_token.idx + len(next_token.text)]
                                ])
                                new_tokens[i] = ("were " if is_plural else "was ")
                                new_tokens[j] = ""  # 移除 'be'
                                target_chars.append([
                                    token.idx, 
                                    token.idx + len("were" if is_plural else "was")
                                ])
                                tokens_modified = True
                                i = j + 1
                                continue
                        else:
                            # 情况 1.3: "will be" + 形容词/副词等
                            is_plural = self._is_subject_plural_for_verb(token)
                            original_chars.extend([
                                [token.idx, token.idx + len(token.text)],
                                [next_token.idx, next_token.idx + len(next_token.text)]
                            ])
                            new_tokens[i] = ("were " if is_plural else "was ")
                            new_tokens[j] = ""  # 移除 'be'
                            target_chars.append([
                                token.idx, 
                                token.idx + len("were" if is_plural else "was")
                            ])
                            tokens_modified = True
                            i = j + 1
                            continue

                    # --- 情况 2: 完成时态 "will have VBN" ---
                    elif next_token.lemma_ == "have":
                        k = j + 1
                        while k < len(doc) and doc[k].pos_ not in ("VERB", "AUX"):
                            k += 1
                        if k < len(doc) and doc[k].tag_ == "VBN":
                            main_verb_token = doc[k]
                            past_form = main_verb_token._.inflect("VBD")
                            if past_form:
                                original_chars.extend([
                                    [token.idx, token.idx + len(token.text)],
                                    [next_token.idx, next_token.idx + len(next_token.text)],
                                    [main_verb_token.idx, main_verb_token.idx + len(main_verb_token.text)]
                                ])
                                new_tokens[i] = ""  # 移除 'will'
                                new_tokens[j] = ""  # 移除 'have'
                                new_tokens[k] = past_form + main_verb_token.whitespace_
                                target_chars.append([
                                    main_verb_token.idx, 
                                    main_verb_token.idx + len(past_form)
                                ])
                                tokens_modified = True
                            i = k + 1
                            continue

                    # --- 情况 3: 简单将来时 "will VERB" ---
                    elif next_token.pos_ == "VERB":
                        past_form = next_token._.inflect("VBD")
                        if past_form:
                            original_chars.extend([
                                [token.idx, token.idx + len(token.text)],
                                [next_token.idx, next_token.idx + len(next_token.text)]
                            ])
                            new_tokens[i] = ""  # 移除 'will'
                            new_tokens[j] = past_form + next_token.whitespace_
                            target_chars.append([
                                next_token.idx, 
                                next_token.idx + len(past_form)
                            ])
                            tokens_modified = True
                        i = j + 1
                        continue

                i += 1

            # --- 检测并转换 "be going to + ..." 结构 (逻辑与 will 类似) ---
            elif token.lemma_ == "be" and i + 2 < len(doc) and \
                    doc[i + 1].text.lower() == "going" and doc[i + 2].text.lower() == "to":
                is_future = True

                j = i + 3
                while j < len(doc) and doc[j].pos_ not in ("VERB", "AUX"):
                    j += 1

                if j < len(doc):
                    next_token = doc[j]

                    # 简单将来时: "be going to VERB" -> "VERB-ed"
                    if next_token.pos_ == "VERB":
                        past_form = next_token._.inflect("VBD")
                        if past_form:
                            original_chars.extend([
                                [token.idx, token.idx + len(token.text)],
                                [doc[i+1].idx, doc[i+1].idx + len(doc[i+1].text)],
                                [doc[i+2].idx, doc[i+2].idx + len(doc[i+2].text)],
                                [next_token.idx, next_token.idx + len(next_token.text)]
                            ])
                            new_tokens[i] = ""       # 移除 'be'
                            new_tokens[i + 1] = ""   # 移除 'going'
                            new_tokens[i + 2] = ""   # 移除 'to'
                            new_tokens[j] = past_form + next_token.whitespace_
                            target_chars.append([
                                next_token.idx, 
                                next_token.idx + len(past_form)
                            ])
                            tokens_modified = True
                        i = j + 1
                        continue

                # 如果 "be going to" 后面没有找到可处理的结构，确保 i 前进
                i += 1

            else:
                # 如果当前 token 不构成将来时结构的开头，则正常前进
                i += 1

        # --- 生成转换后的句子 ---
        converted_text = original_text
        if tokens_modified:
            converted_text = "".join(new_tokens)

        # 返回结果
        return {
            "original_text": original_text,
            "original_char": original_chars,
            "is_future_tense": is_future,
            "target_char": target_chars,
            "target_text": converted_text
        }



_converter = None


def _get_converter() -> TenseConverter:
    global _converter
    if _converter is None:
        _converter = TenseConverter()
    return _converter


def convert_future_to_past(text: str) -> dict:
    """对输入文本执行将来时到过去时的转换，返回逐句结果列表。"""
    result = _get_converter().process_text(text)
    return result


