#!/usr/bin/env python3.11
"""
内存管理器
提供内存监控、限制、预警和自动清理功能
"""

import gc
import os
import psutil
import threading
import time
import warnings
from typing import Dict, Any, Optional, Callable
from contextlib import contextmanager

from logger.logger import app_logger


class MemoryManager:
    """内存管理器"""
    
    def __init__(self):
        self.process = psutil.Process()
        self._memory_limit_mb = None
        self._warning_threshold = 0.8  # 80% 时发出警告
        self._critical_threshold = 0.9  # 90% 时强制清理
        self._monitoring_enabled = False
        self._monitor_thread = None
        self._monitor_interval = 30  # 30秒检查一次
        self._callbacks = {
            'warning': [],
            'critical': [],
            'limit_exceeded': []
        }
    
    def set_memory_limit(self, limit_mb: int):
        """
        设置内存限制
        
        Args:
            limit_mb: 内存限制（MB）
        """
        self._memory_limit_mb = limit_mb
        app_logger.info(f"设置内存限制: {limit_mb} MB")
    
    def get_memory_usage(self) -> Dict[str, float]:
        """
        获取当前内存使用情况
        
        Returns:
            Dict: 内存使用信息
        """
        memory_info = self.process.memory_info()
        memory_percent = self.process.memory_percent()
        
        return {
            'rss_mb': memory_info.rss / 1024 / 1024,  # 物理内存
            'vms_mb': memory_info.vms / 1024 / 1024,  # 虚拟内存
            'percent': memory_percent,  # 占系统总内存的百分比
            'available_mb': psutil.virtual_memory().available / 1024 / 1024,
            'limit_mb': self._memory_limit_mb,
            'limit_usage_ratio': (memory_info.rss / 1024 / 1024) / self._memory_limit_mb if self._memory_limit_mb else None
        }
    
    def check_memory_status(self) -> Dict[str, Any]:
        """
        检查内存状态
        
        Returns:
            Dict: 内存状态信息
        """
        usage = self.get_memory_usage()
        status = {
            'usage': usage,
            'status': 'normal',
            'message': '',
            'action_needed': False
        }
        
        if self._memory_limit_mb:
            ratio = usage['limit_usage_ratio']
            
            if ratio >= self._critical_threshold:
                status['status'] = 'critical'
                status['message'] = f'内存使用达到临界值 ({ratio:.1%})'
                status['action_needed'] = True
            elif ratio >= self._warning_threshold:
                status['status'] = 'warning'
                status['message'] = f'内存使用较高 ({ratio:.1%})'
                status['action_needed'] = False
        
        return status
    
    def force_gc(self, rounds: int = 3) -> Dict[str, int]:
        """
        强制垃圾回收
        
        Args:
            rounds: 回收轮数
            
        Returns:
            Dict: 回收统计信息
        """
        app_logger.info(f"开始强制垃圾回收 ({rounds} 轮)")
        
        before_usage = self.get_memory_usage()
        collected_objects = 0
        
        for i in range(rounds):
            collected = gc.collect()
            collected_objects += collected
            app_logger.debug(f"第 {i+1} 轮垃圾回收: {collected} 个对象")
            time.sleep(0.1)  # 短暂等待
        
        after_usage = self.get_memory_usage()
        freed_mb = before_usage['rss_mb'] - after_usage['rss_mb']
        
        result = {
            'collected_objects': collected_objects,
            'freed_mb': freed_mb,
            'before_mb': before_usage['rss_mb'],
            'after_mb': after_usage['rss_mb']
        }
        
        app_logger.info(f"垃圾回收完成: 回收 {collected_objects} 个对象, 释放 {freed_mb:.2f} MB")
        return result
    
    def add_callback(self, event_type: str, callback: Callable):
        """
        添加内存事件回调
        
        Args:
            event_type: 事件类型 ('warning', 'critical', 'limit_exceeded')
            callback: 回调函数
        """
        if event_type in self._callbacks:
            self._callbacks[event_type].append(callback)
    
    def _trigger_callbacks(self, event_type: str, data: Dict[str, Any]):
        """触发回调函数"""
        for callback in self._callbacks.get(event_type, []):
            try:
                callback(data)
            except Exception as e:
                app_logger.error(f"内存事件回调执行失败: {e}")
    
    def start_monitoring(self, interval: int = 30):
        """
        启动内存监控
        
        Args:
            interval: 监控间隔（秒）
        """
        if self._monitoring_enabled:
            return
        
        self._monitor_interval = interval
        self._monitoring_enabled = True
        
        def monitor_loop():
            app_logger.info("内存监控已启动")
            
            while self._monitoring_enabled:
                try:
                    status = self.check_memory_status()
                    
                    if status['status'] == 'critical':
                        app_logger.warning(f"内存使用临界: {status['message']}")
                        self._trigger_callbacks('critical', status)
                        
                        # 自动执行垃圾回收
                        self.force_gc()
                        
                    elif status['status'] == 'warning':
                        app_logger.info(f"内存使用警告: {status['message']}")
                        self._trigger_callbacks('warning', status)
                    
                    # 检查是否超过限制
                    if (self._memory_limit_mb and 
                        status['usage']['rss_mb'] > self._memory_limit_mb):
                        app_logger.error(f"内存使用超过限制: {status['usage']['rss_mb']:.2f} MB > {self._memory_limit_mb} MB")
                        self._trigger_callbacks('limit_exceeded', status)
                    
                except Exception as e:
                    app_logger.error(f"内存监控异常: {e}")
                
                time.sleep(self._monitor_interval)
        
        self._monitor_thread = threading.Thread(target=monitor_loop, daemon=True)
        self._monitor_thread.start()
    
    def stop_monitoring(self):
        """停止内存监控"""
        if self._monitoring_enabled:
            self._monitoring_enabled = False
            app_logger.info("内存监控已停止")
    
    @contextmanager
    def memory_limit_context(self, limit_mb: int, auto_gc: bool = True):
        """
        内存限制上下文管理器
        
        Args:
            limit_mb: 内存限制（MB）
            auto_gc: 是否自动垃圾回收
        """
        old_limit = self._memory_limit_mb
        self.set_memory_limit(limit_mb)
        
        try:
            yield self
        finally:
            if auto_gc:
                self.force_gc()
            self._memory_limit_mb = old_limit
    
    def get_memory_stats(self) -> Dict[str, Any]:
        """
        获取详细的内存统计信息
        
        Returns:
            Dict: 内存统计信息
        """
        usage = self.get_memory_usage()
        
        # 获取垃圾回收统计
        gc_stats = {
            'collections': gc.get_count(),
            'thresholds': gc.get_threshold(),
            'stats': gc.get_stats() if hasattr(gc, 'get_stats') else None
        }
        
        # 获取系统内存信息
        system_memory = psutil.virtual_memory()
        
        return {
            'process_memory': usage,
            'system_memory': {
                'total_mb': system_memory.total / 1024 / 1024,
                'available_mb': system_memory.available / 1024 / 1024,
                'percent': system_memory.percent,
                'used_mb': system_memory.used / 1024 / 1024,
                'free_mb': system_memory.free / 1024 / 1024
            },
            'gc_stats': gc_stats,
            'monitoring_enabled': self._monitoring_enabled,
            'memory_limit_mb': self._memory_limit_mb
        }


# 全局内存管理器实例
memory_manager = MemoryManager()


def get_memory_usage() -> Dict[str, float]:
    """便捷函数：获取内存使用情况"""
    return memory_manager.get_memory_usage()


def force_gc(rounds: int = 3) -> Dict[str, int]:
    """便捷函数：强制垃圾回收"""
    return memory_manager.force_gc(rounds)


@contextmanager
def memory_limit(limit_mb: int, auto_gc: bool = True):
    """便捷函数：内存限制上下文管理器"""
    with memory_manager.memory_limit_context(limit_mb, auto_gc):
        yield


def start_memory_monitoring(interval: int = 30):
    """便捷函数：启动内存监控"""
    memory_manager.start_monitoring(interval)


def stop_memory_monitoring():
    """便捷函数：停止内存监控"""
    memory_manager.stop_monitoring()


# 程序退出时自动停止监控
import atexit
atexit.register(stop_memory_monitoring)
