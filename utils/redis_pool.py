#!/usr/bin/env python3.11
"""
Redis 连接池管理器
提供统一的 Redis 连接池管理，防止连接泄露
"""

import threading
from typing import Any, Dict

import redis
from redis.connection import ConnectionPool

from logger.logger import app_logger


class RedisPoolManager:
    """Redis 连接池管理器 - 单例模式"""

    _instance = None
    _lock = threading.RLock()
    _pools: Dict[str, ConnectionPool] = {}

    def __new__(cls):
        if cls._instance is None:
            with cls._lock:
                if cls._instance is None:
                    cls._instance = super().__new__(cls)
        return cls._instance

    def __init__(self):
        if not hasattr(self, "_initialized"):
            self._initialized = True
            app_logger.info("Redis 连接池管理器初始化")

    def get_pool(
        self,
        pool_name: str = "default",
        host: str = "***********",
        port: int = 6379,
        db: int = 4,
        password: str = "",
        max_connections: int = 20,
        socket_connect_timeout: int = 10,
        socket_timeout: int = 30,
        retry_on_timeout: bool = True,
        health_check_interval: int = 30,
    ) -> ConnectionPool:
        """
        获取或创建连接池

        Args:
            pool_name: 连接池名称
            host: Redis 主机
            port: Redis 端口
            db: Redis 数据库
            password: Redis 密码
            max_connections: 最大连接数
            socket_connect_timeout: 连接超时
            socket_timeout: 读写超时
            retry_on_timeout: 超时重试
            health_check_interval: 健康检查间隔

        Returns:
            ConnectionPool: Redis 连接池
        """
        with self._lock:
            if pool_name not in self._pools:
                app_logger.info(f"创建 Redis 连接池: {pool_name} -> {host}:{port}/{db}")

                self._pools[pool_name] = ConnectionPool(
                    host=host,
                    port=port,
                    db=db,
                    password=password,
                    decode_responses=True,
                    max_connections=max_connections,
                    socket_connect_timeout=socket_connect_timeout,
                    socket_timeout=socket_timeout,
                    retry_on_timeout=retry_on_timeout,
                    health_check_interval=health_check_interval,
                )

            return self._pools[pool_name]

    def get_redis_client(self, pool_name: str = "default", **pool_kwargs) -> redis.Redis:
        """
        获取 Redis 客户端

        Args:
            pool_name: 连接池名称
            **pool_kwargs: 连接池参数

        Returns:
            redis.Redis: Redis 客户端
        """
        pool = self.get_pool(pool_name, **pool_kwargs)
        return redis.Redis(connection_pool=pool)

    def close_pool(self, pool_name: str):
        """
        关闭指定连接池

        Args:
            pool_name: 连接池名称
        """
        with self._lock:
            if pool_name in self._pools:
                try:
                    self._pools[pool_name].disconnect()
                    del self._pools[pool_name]
                    app_logger.info(f"Redis 连接池已关闭: {pool_name}")
                except Exception as e:
                    app_logger.error(f"关闭 Redis 连接池失败 {pool_name}: {e}")

    def close_all_pools(self):
        """关闭所有连接池"""
        with self._lock:
            for pool_name in list(self._pools.keys()):
                self.close_pool(pool_name)
            app_logger.info("所有 Redis 连接池已关闭")

    def get_pool_stats(self) -> Dict[str, Dict[str, Any]]:
        """
        获取连接池统计信息

        Returns:
            Dict: 连接池统计信息
        """
        stats = {}
        with self._lock:
            for pool_name, pool in self._pools.items():
                try:
                    stats[pool_name] = {
                        "max_connections": pool.max_connections,
                        "created_connections": pool.created_connections,
                        "available_connections": len(pool._available_connections),
                        "in_use_connections": len(pool._in_use_connections),
                    }
                except Exception as e:
                    stats[pool_name] = {"error": str(e)}
        return stats

    def health_check(self) -> Dict[str, bool]:
        """
        健康检查所有连接池

        Returns:
            Dict: 健康检查结果
        """
        results = {}
        with self._lock:
            for pool_name, pool in self._pools.items():
                try:
                    client = redis.Redis(connection_pool=pool)
                    client.ping()
                    results[pool_name] = True
                    app_logger.debug(f"Redis 连接池健康检查通过: {pool_name}")
                except Exception as e:
                    results[pool_name] = False
                    app_logger.warning(f"Redis 连接池健康检查失败 {pool_name}: {e}")
        return results


# 全局连接池管理器实例
redis_pool_manager = RedisPoolManager()


def get_redis_client(pool_name: str = "default", **kwargs) -> redis.Redis:
    """
    便捷函数：获取 Redis 客户端

    Args:
        pool_name: 连接池名称
        **kwargs: 连接池参数

    Returns:
        redis.Redis: Redis 客户端
    """
    # 如果没有提供配置参数，使用默认的 Redis 配置
    if not kwargs:
        try:
            from configurer.config_reader import get_redis_config

            config = get_redis_config()
            kwargs = {
                "host": config["host"],
                "port": config["port"],
                "db": config["db"],
                "password": config["password"],
            }
        except Exception as e:
            app_logger.warning(f"获取默认 Redis 配置失败，使用硬编码配置: {e}")
            kwargs = {
                "host": "***********",
                "port": 6379,
                "db": 4,
                "password": "wtg2024@",
            }

    return redis_pool_manager.get_redis_client(pool_name, **kwargs)


def close_redis_pools():
    """便捷函数：关闭所有连接池"""
    redis_pool_manager.close_all_pools()


def get_redis_pool_status():
    """
    便捷函数：获取 Redis 连接池状态摘要

    Returns:
        Dict: 连接池状态摘要
    """
    try:
        pool_stats = redis_pool_manager.get_pool_stats()
        health_results = redis_pool_manager.health_check()

        # 计算总体健康状态
        total_pools = len(health_results)
        healthy_pools = sum(1 for is_healthy in health_results.values() if is_healthy)

        # 检查连接使用率
        high_usage_pools = []
        for pool_name, stats in pool_stats.items():
            if "error" not in stats and stats["max_connections"] > 0:
                usage_ratio = stats["in_use_connections"] / stats["max_connections"]
                if usage_ratio > 0.8:  # 使用率超过80%
                    high_usage_pools.append(
                        {
                            "pool": pool_name,
                            "usage_ratio": usage_ratio,
                            "in_use": stats["in_use_connections"],
                            "max": stats["max_connections"],
                        }
                    )

        return {
            "status": "healthy" if healthy_pools == total_pools and not high_usage_pools else "warning",
            "total_pools": total_pools,
            "healthy_pools": healthy_pools,
            "high_usage_pools": high_usage_pools,
            "pool_stats": pool_stats,
            "health_results": health_results,
        }
    except Exception as e:
        return {"status": "error", "error": str(e)}


# 程序退出时自动清理连接池
import atexit

atexit.register(close_redis_pools)
