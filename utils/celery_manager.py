# utils/celery_manager.py

import os
import time
from typing import Dict, List

import psutil

from logger.logger import app_logger


class CeleryWorkerManager:
    """Celery Worker 管理器 - 专注于监控和诊断，不直接管理Worker进程

    Worker进程由Circus管理，此类提供：
    1. 健康检查和诊断
    2. 进程信息收集
    3. 紧急情况下的进程清理
    """

    def __init__(self):
        self.app_name = "celery_task"
        # 不再生成worker_name，因为不直接启动Worker
        # self.worker_name = self._generate_unique_worker_name()
        # self.pid_file = "/tmp/celery_worker.pid"

    def get_circus_managed_workers(self) -> List[Dict]:
        """获取由Circus管理的Celery Worker信息"""
        try:
            # 通过进程信息识别Circus启动的Worker
            circus_workers = []
            for proc_info in self.get_celery_processes():
                cmdline = proc_info.get("cmdline", "")
                # 识别Circus启动的Worker（包含特定的命令行参数）
                if "--without-gossip" in cmdline and "--without-mingle" in cmdline and "--without-heartbeat" in cmdline:
                    circus_workers.append(proc_info)

            app_logger.debug(f"发现 {len(circus_workers)} 个Circus管理的Worker")
            return circus_workers

        except Exception as e:
            app_logger.error(f"获取Circus管理的Worker失败: {e}")
            return []

    def get_celery_processes(self) -> List[Dict]:
        """获取所有真正的 Celery Worker 进程（排除辅助进程）"""
        processes = []
        try:
            for proc in psutil.process_iter(["pid", "ppid", "name", "cmdline", "create_time", "status"]):
                try:
                    cmdline = " ".join(proc.info["cmdline"]) if proc.info["cmdline"] else ""

                    # 更精确的 Celery Worker 进程检测
                    is_celery_worker = (
                        "celery" in cmdline.lower()
                        and "worker" in cmdline.lower()
                        and proc.info["name"] in ["python3.11", "python", "python3", "celery"]
                        and (
                            "/usr/local/python3.11/bin/celery" in cmdline
                            or "python3.11/bin/celery" in cmdline
                            or cmdline.startswith("celery ")  # 直接以 celery 开头的命令
                        )
                        and "-A celery_task worker" in cmdline
                        and "bash -c" not in cmdline  # 排除启动脚本
                        and "python3.11 -c" not in cmdline  # 排除检查脚本
                        and "celery_realtime_monitor.sh" not in cmdline  # 排除监控脚本
                    )

                    if is_celery_worker:
                        processes.append(
                            {
                                "pid": proc.info["pid"],
                                "ppid": proc.info["ppid"],
                                "name": proc.info["name"],
                                "cmdline": cmdline,
                                "create_time": proc.info["create_time"],
                                "status": proc.info["status"],
                                "running_time": time.time() - proc.info["create_time"],
                            }
                        )
                except (psutil.NoSuchProcess, psutil.AccessDenied):
                    continue
        except Exception as e:
            app_logger.error(f"获取 Celery 进程失败: {e}")

        return processes

    def emergency_cleanup(self) -> bool:
        """紧急清理异常的Celery进程（仅在Circus重启失败时使用）"""
        try:
            app_logger.warning("🚨 执行紧急清理模式...")

            # 只清理非Circus管理的异常进程
            all_processes = self.get_celery_processes()
            circus_workers = self.get_circus_managed_workers()
            circus_pids = {worker["pid"] for worker in circus_workers}

            abnormal_processes = [proc for proc in all_processes if proc["pid"] not in circus_pids]

            if not abnormal_processes:
                app_logger.info("✅ 没有发现异常进程")
                return True

            app_logger.warning(f"发现 {len(abnormal_processes)} 个异常进程，将被清理")

            # 清理异常进程
            for proc_info in abnormal_processes:
                try:
                    proc = psutil.Process(proc_info["pid"])
                    app_logger.warning(f"清理异常进程 {proc_info['pid']}: {proc_info['cmdline'][:100]}...")
                    proc.terminate()
                    time.sleep(1)

                    try:
                        proc.kill()  # 强制杀死
                    except (psutil.NoSuchProcess, psutil.AccessDenied):
                        pass

                    app_logger.info(f"✅ 异常进程 {proc_info['pid']} 已清理")

                except (psutil.NoSuchProcess, psutil.AccessDenied) as e:
                    app_logger.warning(f"进程 {proc_info['pid']} 已不存在: {e}")

            app_logger.info("✅ 紧急清理完成，等待Circus自动重启...")
            return True

        except Exception as e:
            app_logger.error(f"紧急清理失败: {e}")
            return False

    def request_circus_restart(self) -> bool:
        """请求Circus重启Celery Worker（推荐方式）"""
        try:
            app_logger.info("🔄 请求Circus重启Celery Worker...")

            # 方法1：通过Circus API重启（先停止再启动，避免重启冲突）
            try:
                import subprocess

                # 先停止Worker
                stop_result = subprocess.run(
                    ["circusctl", "stop", "yiya-ai-bot-celery"], capture_output=True, text=True, timeout=30
                )

                if stop_result.returncode == 0:
                    app_logger.info("✅ Circus停止命令执行成功")
                    time.sleep(3)  # 等待进程完全停止

                    # 再启动Worker
                    start_result = subprocess.run(
                        ["circusctl", "start", "yiya-ai-bot-celery"], capture_output=True, text=True, timeout=30
                    )

                    if start_result.returncode == 0:
                        app_logger.info("✅ Circus启动命令执行成功")
                        return True
                    else:
                        app_logger.warning(f"Circus启动命令失败: {start_result.stderr}")
                else:
                    app_logger.warning(f"Circus停止命令失败: {stop_result.stderr}")

            except Exception as e:
                app_logger.warning(f"Circus API重启失败: {e}")

            # 方法2：发送信号给Circus进程（备用方案）
            try:
                # 查找Circus管理的Worker进程
                circus_workers = self.get_circus_managed_workers()
                if circus_workers:
                    app_logger.info("发送TERM信号给Circus管理的Worker，让Circus自动重启")
                    for worker in circus_workers:
                        os.kill(worker["pid"], 15)  # SIGTERM
                    return True
                else:
                    app_logger.warning("未找到Circus管理的Worker进程")
                    return False

            except Exception as e:
                app_logger.error(f"信号重启失败: {e}")
                return False

        except Exception as e:
            app_logger.error(f"请求Circus重启失败: {e}")
            return False

    def restart_worker(self) -> bool:
        """重启 Celery Worker - 优先使用Circus管理"""
        app_logger.info("🔄 开始重启 Celery Worker...")

        # 优先使用Circus重启（推荐方式）
        if self.request_circus_restart():
            app_logger.info("✅ 通过Circus重启成功")
            return True

        # 如果Circus重启失败，使用紧急清理模式
        app_logger.warning("Circus重启失败，使用紧急清理模式")

        # 1. 清理异常进程
        if not self.emergency_cleanup():
            app_logger.error("❌ 紧急清理失败")
            return False

        # 2. 等待Circus自动重启
        app_logger.info("等待Circus自动重启Worker...")
        time.sleep(10)

        # 3. 验证重启是否成功
        circus_workers = self.get_circus_managed_workers()
        if circus_workers:
            app_logger.info("✅ Circus自动重启成功")
            return True
        else:
            app_logger.error("❌ Circus自动重启失败")
            return False

    def check_node_name_conflicts(self) -> Dict:
        """检查节点名冲突"""
        try:
            from celery_task.celery import celery_app

            inspect = celery_app.control.inspect()
            active_workers = inspect.active() or {}
            registered_workers = inspect.registered() or {}

            # 获取所有节点名
            all_nodes = set()
            all_nodes.update(active_workers.keys())
            all_nodes.update(registered_workers.keys())

            # 检查是否有重复的基础节点名（去掉时间戳后的部分）
            base_names = {}
            for node in all_nodes:
                # 提取基础名称（去掉时间戳和随机后缀）
                if "@" in node:
                    base_part = node.split("@")[1].split("-")[0] if "-" in node.split("@")[1] else node.split("@")[1]
                    if base_part in base_names:
                        base_names[base_part].append(node)
                    else:
                        base_names[base_part] = [node]

            conflicts = {k: v for k, v in base_names.items() if len(v) > 1}

            return {
                "has_conflicts": len(conflicts) > 0,
                "conflicts": conflicts,
                "total_nodes": len(all_nodes),
                "all_nodes": list(all_nodes),
            }

        except Exception as e:
            app_logger.error(f"检查节点名冲突失败: {e}")
            return {"has_conflicts": False, "error": str(e)}

    def check_worker_health(self) -> Dict:
        """简化的Celery Worker健康检查 - 只检查真正的问题"""
        try:
            from celery_task.celery import celery_app

            health_status = {
                "timestamp": time.time(),
                "is_healthy": True,
                "issues": [],
                "health_score": 100,
            }

            # 1. 检查Celery连接是否正常
            try:
                inspect = celery_app.control.inspect()
                active = inspect.active()

                if active is None:
                    health_status["is_healthy"] = False
                    health_status["issues"].append("无法连接到Celery Broker")
                    health_status["health_score"] = 0
                    return health_status

            except Exception as e:
                health_status["is_healthy"] = False
                health_status["issues"].append(f"Celery连接失败: {str(e)}")
                health_status["health_score"] = 0
                return health_status

            # 2. 检查是否有Worker在运行
            try:
                if not active:
                    health_status["is_healthy"] = False
                    health_status["issues"].append("没有活跃的Worker")
                    health_status["health_score"] = 0
                    return health_status

            except Exception as e:
                health_status["is_healthy"] = False
                health_status["issues"].append(f"Worker状态检查失败: {str(e)}")
                health_status["health_score"] = 0
                return health_status

            # 3. 检查Worker是否能响应（可选检查，不影响健康状态）
            try:
                stats = inspect.stats()
                if not stats:
                    app_logger.warning("Worker统计信息获取失败，但不影响健康状态")

            except Exception as e:
                app_logger.warning(f"Worker统计检查失败: {str(e)}，但不影响健康状态")

            # 如果所有关键检查都通过，认为是健康的
            app_logger.debug("Celery Worker健康检查通过")
            return health_status

        except Exception as e:
            app_logger.error(f"健康检查异常: {e}")
            return {
                "timestamp": time.time(),
                "is_healthy": False,
                "issues": [f"健康检查异常: {str(e)}"],
                "health_score": 0,
            }


# 全局实例
celery_manager = CeleryWorkerManager()


def ensure_single_worker() -> bool:
    """确保只有一个 Celery Worker 运行"""
    try:
        health = celery_manager.check_worker_health()

        app_logger.info(f"Worker 健康检查: 分数 {health['health_score']}/100")

        # 如果健康分数低于 80，重启 Worker
        if health["health_score"] < 80:
            app_logger.warning(f"Worker 健康状态不佳 (分数: {health['health_score']})，开始重启...")
            return celery_manager.restart_worker()
        else:
            app_logger.info("✅ Worker 健康状态良好")
            return True

    except Exception as e:
        app_logger.error(f"确保单一 Worker 失败: {e}")
        return False


def check_connection_quality() -> Dict:
    """简化的连接质量检查 - 移除复杂的性能检测"""
    # 连接质量检查已简化，避免因网络延迟导致误判
    return {
        "quality_score": 100,
        "issues": [],
        "timestamp": time.time(),
    }


def get_worker_status() -> Dict:
    """获取 Worker 状态（用于监控）- 简化版本"""
    health = celery_manager.check_worker_health()

    # 不再使用复杂的连接质量检查
    # 只返回基本的健康状态
    return health
