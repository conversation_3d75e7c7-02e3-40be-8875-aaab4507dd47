# -*- coding: utf-8 -*-
# utils/oss_utils.py

import hashlib
import io
import os
import time
from concurrent.futures import ThreadPoolExecutor, as_completed
from datetime import datetime
from typing import Any, Callable, Dict, Optional

import oss2
from oss2.models import GetObjectResult

from logger.logger import app_logger

LASER = "/data/LASER_yiya"

# 从配置文件中获取Access Key ID和Access Key Secret
access_key_id = "LTAI5tSMtzYQ5GVPCm8njDYp"
access_key_secret = "******************************"

# 对于500MB的文件，可以设置每片10MB到50MB
PART_SIZE = 20 * 1024 * 1024  # 10MB

# 使用获取的RAM用户的访问密钥配置访问凭证
auth = oss2.AuthV4(access_key_id, access_key_secret)

# 使用环境变量中获取的RAM用户访问密钥配置访问凭证
bucket_name = "yiya-dev"  # 将 bucket 名称存为变量
bucket = oss2.Bucket(auth, "https://oss-cn-hangzhou.aliyuncs.com", bucket_name, region="cn-hangzhou")


def write_large_file_from_stream(
    stream: io.BytesIO, key: str, progress_callback: Optional[Callable[[int, int], None]] = None
):
    """
    从内存流中上传文件到OSS。
    该方法会自动判断文件大小并决定是否使用分片上传，是官方推荐的方式。
    """
    stream.seek(0)
    bucket.put_object(key, stream, progress_callback=progress_callback)


def write_large_file_from_stream_parallel(
    stream: io.BytesIO,
    key: str,
    part_size: int = 20 * 1024 * 1024,
    max_workers: int = 2,
    progress_callback: Optional[Callable[[int, int], None]] = None,
):
    """
    优化的并行分片上传大文件到OSS，解决内存占用和网络瓶颈问题。

    Args:
        stream: 内存流对象
        key: OSS文件key
        part_size: 分片大小(字节), 默认5MB（更小分片=更高并发）
        max_workers: 最大并发线程数, 默认4
        progress_callback: 进度回调函数, 参数为(bytes_uploaded, total_bytes)

    Returns:
        上传结果
    """

    stream.seek(0, io.SEEK_END)
    total_size = stream.tell()
    stream.seek(0)

    if total_size <= 10 * 1024 * 1024:  # 小于10MB直接上传
        return bucket.put_object(key, stream, progress_callback=progress_callback)

    # 计算最优分片大小和并发数
    optimal_part_size = max(part_size, min(8 * 1024 * 1024, total_size // 50))  # 至少50个分片
    part_count = (total_size + optimal_part_size - 1) // optimal_part_size

    # 基于CPU核心数限制并发
    actual_max_workers = min(max_workers, (os.cpu_count() or 4) * 2)

    upload_id = bucket.init_multipart_upload(key).upload_id

    try:
        # 预计算所有分片信息（不加载数据到内存）
        parts_info = []
        for part_number in range(1, part_count + 1):
            start = (part_number - 1) * optimal_part_size
            end = min(start + optimal_part_size, total_size)
            parts_info.append((part_number, start, end - start))

        uploaded_parts = []
        uploaded_bytes = 0

        # 使用线程池和内存映射优化
        with ThreadPoolExecutor(max_workers=actual_max_workers) as executor:
            future_to_part = {
                executor.submit(_upload_part_from_stream, stream, key, upload_id, part_num, start, size): part_num
                for part_num, start, size in parts_info
            }

            for future in as_completed(future_to_part):
                part_num = future_to_part[future]
                try:
                    result = future.result()
                    uploaded_parts.append(result)
                    uploaded_bytes += parts_info[part_num - 1][2]

                    # 进度回调
                    if progress_callback:
                        progress_callback(uploaded_bytes, total_size)

                except Exception as e:
                    bucket.abort_multipart_upload(key, upload_id)
                    raise e

        # 完成分片上传
        uploaded_parts.sort(key=lambda x: x.part_number)
        return bucket.complete_multipart_upload(key, upload_id, uploaded_parts)

    except Exception as e:
        try:
            bucket.abort_multipart_upload(key, upload_id)
            app_logger.info(f"已取消多部分上传: {key}")
        except Exception as abort_error:
            app_logger.warning(f"取消多部分上传失败: {abort_error}")
        raise e


def _upload_part_from_stream(stream: io.BytesIO, key: str, upload_id: str, part_number: int, start: int, size: int):
    """从流中读取指定位置的数据并上传分片"""
    stream.seek(start)
    data = stream.read(size)
    result = bucket.upload_part(key, upload_id, part_number, data)
    return oss2.models.PartInfo(part_number, result.etag)


def _upload_part(key: str, upload_id: str, part_number: int, data: bytes):
    """上传单个分片"""
    result = bucket.upload_part(key, upload_id, part_number, data)
    return oss2.models.PartInfo(part_number, result.etag)


def download_file(key, save_path):
    """将文件从OSS下载到本地路径。"""
    folder = os.path.dirname(save_path)
    if not os.path.exists(folder):
        os.makedirs(folder)
    bucket.get_object_to_file(key, save_path)


def write_file(local_path, key):
    """从本地文件路径上传文件到OSS。"""
    bucket.put_object_from_file(key, local_path)


def get_file_url(key: str):
    """获取文件的签名URL。"""
    expiration = 24 * 60 * 60
    return bucket.sign_url("GET", key, expiration)


def progress_callback(bytes_uploaded, total_bytes):
    """默认进度回调函数"""
    progress = (bytes_uploaded / total_bytes) * 100
    print(f"\r上传进度: {progress:.2f}% ({bytes_uploaded}/{total_bytes} bytes)", end="", flush=True)
    if bytes_uploaded == total_bytes:
        print("\n上传完成!")


def download_file_to_stream(key: str) -> GetObjectResult:
    """
    从OSS下载文件，并以流的形式返回。
    返回的对象本身就是一个可读的流 (file-like object)。
    """
    return bucket.get_object(key)


def write_from_stream(stream: io.BytesIO, key: str):
    """
    从内存中的流（比如 io.BytesIO）上传文件到OSS。
    """
    # put_object 方法可以直接处理流对象
    bucket.put_object(key, stream)


def test_upload_speed(file_size_mb=50, optimized=True):
    """测试上传速度并诊断瓶颈"""
    import io

    # 创建测试数据
    test_data = b"x" * (file_size_mb * 1024 * 1024)
    stream = io.BytesIO(test_data)
    key = f"speed_test_{file_size_mb}mb.dat"

    print(f"=== 上传速度测试 ({file_size_mb}MB) ===")

    # 测试简单上传
    start = time.time()
    write_large_file_from_stream(stream, key)
    simple_time = time.time() - start
    print(f"简单上传: {simple_time:.2f}s ({file_size_mb / simple_time:.2f} MB/s)")

    # 测试并行上传（优化参数）
    stream.seek(0)
    start = time.time()
    if optimized:
        write_large_file_from_stream_parallel(stream, key + "_optimized", part_size=20 * 1024 * 1024, max_workers=2)
    else:
        write_large_file_from_stream_parallel(stream, key + "_parallel")

    parallel_time = time.time() - start
    print(f"并行上传: {parallel_time:.2f}s ({file_size_mb / parallel_time:.2f} MB/s)")

    # 带宽分析
    simple_bandwidth = file_size_mb / simple_time
    parallel_bandwidth = file_size_mb / parallel_time

    print("\n=== 网络分析 ===")
    print(f"简单上传带宽: {simple_bandwidth:.1f} MB/s ({simple_bandwidth * 8:.0f} Mbps)")
    print(f"并行上传带宽: {parallel_bandwidth:.1f} MB/s ({parallel_bandwidth * 8:.0f} Mbps)")

    if simple_bandwidth > 10:
        print("网络带宽充足")
    else:
        print("网络带宽可能成为瓶颈")

    # 优化建议
    print("\n=== 优化建议 ===")
    if parallel_time > simple_time * 1.1:
        print("建议使用大分片+少线程：part_size=20MB, max_workers=2")
        print("或直接使用简单上传")
    elif parallel_time > simple_time:
        print("建议使用中等分片：part_size=10MB, max_workers=2")
    else:
        print("当前配置合理，可继续使用并行上传")

    # 清理测试文件
    try:
        bucket.delete_object(key)
        bucket.delete_object(key + "_parallel")
    except:
        pass


# ===== 新增功能：整合自 oss_uploader.py =====


def generate_image_key(image_data: bytes, file_extension: str = "png") -> str:
    """
    生成图片的OSS存储键名

    Args:
        image_data: 图片二进制数据
        file_extension: 文件扩展名

    Returns:
        OSS存储键名
    """

    # 使用图片内容的MD5哈希作为文件名，避免重复上传
    md5_hash = hashlib.md5(image_data).hexdigest()

    # 按日期组织目录结构
    date_prefix = datetime.now().strftime("%Y/%m/%d")

    # 生成最终的键名
    key = f"protocol-images/{date_prefix}/{md5_hash}.{file_extension}"

    return key


def upload_image(image_data: bytes, file_extension: str = "png") -> Optional[str]:
    """
    上传图片到OSS

    Args:
        image_data: 图片二进制数据
        file_extension: 文件扩展名

    Returns:
        图片的OSS URL，失败返回None
    """
    try:
        # 生成存储键名
        key = generate_image_key(image_data, file_extension)

        # 检查文件是否已存在
        if bucket.object_exists(key):
            app_logger.info(f"图片已存在，跳过上传: {key}")
        else:
            # 上传图片
            result = bucket.put_object(key, image_data)
            if result.status != 200:
                app_logger.error(f"图片上传失败: {key}, 状态码: {result.status}")
                return None
            app_logger.info(f"图片上传成功: {key}")

        # 生成访问URL
        url = f"https://{bucket_name}.oss-cn-hangzhou.aliyuncs.com/{key}"
        return url

    except Exception as e:
        app_logger.error(f"上传图片到OSS失败: {e}")
        return None


def upload_base64_image(base64_data: str) -> Optional[str]:
    """
    上传Base64编码的图片到OSS

    Args:
        base64_data: Base64编码的图片数据

    Returns:
        图片的OSS URL，失败返回None
    """
    try:
        import base64

        # 解析Base64数据
        if base64_data.startswith("data:image/"):
            # 格式: data:image/png;base64,iVBORw0KGgoAAAANSUhEUgAA...
            header, data = base64_data.split(",", 1)
            mime_type = header.split(";")[0].split(":")[1]
            file_extension = mime_type.split("/")[1]
        else:
            # 纯Base64数据，默认为PNG
            data = base64_data
            file_extension = "png"

        # 解码Base64数据
        image_data = base64.b64decode(data)

        # 上传图片
        return upload_image(image_data, file_extension)

    except Exception as e:
        app_logger.error(f"处理Base64图片失败: {e}")
        return None


def upload_json_data(data: Dict[str, Any], prefix: str = "protocols") -> Optional[str]:
    """
    上传JSON数据到OSS

    Args:
        data: 要上传的数据字典
        prefix: OSS存储前缀

    Returns:
        OSS存储键名，失败返回None
    """
    try:
        import json
        import random
        import time

        # 序列化JSON数据
        json_data = json.dumps(data, ensure_ascii=False, indent=2).encode("utf-8")

        # 生成存储键名
        timestamp_ms = int(time.time() * 1000)
        random_suffix = random.randint(100000, 999999)
        key = f"{prefix}/oss-{timestamp_ms}-{random_suffix}.json"

        # 上传数据
        result = bucket.put_object(key, json_data)
        if result.status == 200:
            app_logger.info(f"JSON数据上传成功: {key}, 大小: {len(json_data)} 字节")
            return key
        else:
            app_logger.error(f"JSON数据上传失败: {key}, 状态码: {result.status}")
            return None

    except Exception as e:
        app_logger.error(f"上传JSON数据到OSS失败: {e}")
        return None


def upload_sections_to_oss(sections_data: list, shared_ooxml_parts: dict = None) -> Optional[str]:
    """
    上传sections数据到OSS

    Args:
        sections_data: sections数据列表
        shared_ooxml_parts: 共享的OOXML部分（可选）

    Returns:
        OSS存储键名，失败返回None
    """
    data_for_oss = {"sections": sections_data}
    if shared_ooxml_parts:
        data_for_oss["shared_ooxml_parts"] = shared_ooxml_parts
    return upload_json_data(data_for_oss, "protocols/sections")


def upload_key_information_to_oss(key_info_data: dict, shared_ooxml_parts: dict = None) -> Optional[str]:
    """
    上传key_information数据到OSS

    Args:
        key_info_data: key_information数据字典
        shared_ooxml_parts: 共享的OOXML部分（可选）

    Returns:
        OSS存储键名，失败返回None
    """
    data_for_oss = {"key_information": key_info_data}
    if shared_ooxml_parts:
        data_for_oss["shared_ooxml_parts"] = shared_ooxml_parts
    return upload_json_data(data_for_oss, "protocols/key_information")


def create_image_placeholder(oss_url: str) -> str:
    """
    创建图片占位符XML，用于Word文档中的图片引用

    Args:
        oss_url: OSS图片URL

    Returns:
        包含OSS地址的占位符XML字符串
    """
    placeholder_xml = f"""<w:p>
    <w:r>
        <w:t>图片已上传到OSS</w:t>
    </w:r>
    <w:r>
        <w:br/>
    </w:r>
    <w:r>
        <w:t>OSS地址: {oss_url}</w:t>
    </w:r>
</w:p>"""
    return placeholder_xml


def create_image_text_placeholder(oss_url: str) -> str:
    """
    创建图片占位符纯文本，用于type="image"的数据块

    Args:
        oss_url: OSS图片URL

    Returns:
        OSS key路径: protocol-images/2025/08/02/abc123.png
    """
    # 从完整的OSS URL中提取key部分
    # 例如: https://bucket.oss-cn-hangzhou.aliyuncs.com/protocol-images/2025/08/02/abc123.png
    # 提取: protocol-images/2025/08/02/abc123.png
    if "aliyuncs.com/" in oss_url:
        oss_key = oss_url.split("aliyuncs.com/", 1)[1]
        return oss_key
    else:
        # 如果URL格式不符合预期，使用完整URL
        return oss_url


def upload_image_to_oss(base64_data: str) -> Dict[str, Any]:
    """
    上传图片到OSS的便捷函数

    Args:
        base64_data: Base64编码的图片数据

    Returns:
        包含上传结果的字典
    """
    result = {"success": False, "oss_url": None, "placeholder_xml": None, "fallback_base64": base64_data}

    # 尝试上传到OSS
    oss_url = upload_base64_image(base64_data)
    if oss_url:
        result["success"] = True
        result["oss_url"] = oss_url
        result["placeholder_xml"] = create_image_placeholder(oss_url)
        app_logger.info(f"图片已上传到OSS: {oss_url}")
    else:
        app_logger.warning("OSS上传失败，使用Base64回退模式")

    return result


if __name__ == "__main__":
    test_upload_speed(50, optimized=True)  # 测试优化后的并行上传
