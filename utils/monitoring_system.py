#!/usr/bin/env python3.11
"""
监控和告警系统
提供性能指标收集、历史数据分析和告警通知功能
"""

import json
import threading
import time
from collections import defaultdict, deque
from dataclasses import dataclass
from typing import Any, Callable, Dict, List, Optional

from logger.logger import app_logger
from utils.concurrency_manager import concurrency_manager
from utils.memory_manager import memory_manager
from utils.redis_pool import get_redis_client


@dataclass
class MetricData:
    """指标数据"""

    timestamp: float
    value: float
    tags: Dict[str, str] = None

    def __post_init__(self):
        if self.tags is None:
            self.tags = {}


@dataclass
class AlertRule:
    """告警规则"""

    name: str
    metric_name: str
    condition: str  # 'gt', 'lt', 'eq', 'gte', 'lte'
    threshold: float
    duration: int = 60  # 持续时间（秒）
    enabled: bool = True
    callback: Optional[Callable] = None


class MetricsCollector:
    """指标收集器"""

    def __init__(self, redis_pool_name: str = "monitoring"):
        self.redis_client = get_redis_client(pool_name=redis_pool_name)
        self._metrics_buffer = defaultdict(deque)
        self._buffer_size = 1000
        self._flush_interval = 30  # 30秒刷新一次
        self._last_flush = time.time()
        self._lock = threading.RLock()

    def record_metric(self, name: str, value: float, tags: Dict[str, str] = None):
        """
        记录指标

        Args:
            name: 指标名称
            value: 指标值
            tags: 标签
        """
        metric = MetricData(timestamp=time.time(), value=value, tags=tags or {})

        with self._lock:
            self._metrics_buffer[name].append(metric)

            # 限制缓冲区大小
            if len(self._metrics_buffer[name]) > self._buffer_size:
                self._metrics_buffer[name].popleft()

        # 定期刷新到 Redis
        if time.time() - self._last_flush > self._flush_interval:
            self._flush_metrics()

    def _flush_metrics(self):
        """刷新指标到 Redis"""
        with self._lock:
            for metric_name, metrics in self._metrics_buffer.items():
                if not metrics:
                    continue

                # 将指标数据序列化并存储到 Redis
                pipeline = self.redis_client.pipeline()

                for metric in metrics:
                    key = f"metrics:{metric_name}"
                    data = {"timestamp": metric.timestamp, "value": metric.value, "tags": json.dumps(metric.tags)}

                    # 使用有序集合存储，按时间戳排序
                    pipeline.zadd(key, {json.dumps(data): metric.timestamp})

                    # 设置过期时间（7天）
                    pipeline.expire(key, 7 * 24 * 3600)

                pipeline.execute()
                metrics.clear()

            self._last_flush = time.time()
            app_logger.debug("指标数据已刷新到 Redis")

    def get_metrics(self, name: str, start_time: float = None, end_time: float = None) -> List[MetricData]:
        """
        获取指标数据

        Args:
            name: 指标名称
            start_time: 开始时间戳
            end_time: 结束时间戳

        Returns:
            List[MetricData]: 指标数据列表
        """
        key = f"metrics:{name}"

        if start_time is None:
            start_time = time.time() - 3600  # 默认1小时
        if end_time is None:
            end_time = time.time()

        # 从 Redis 获取数据
        raw_data = self.redis_client.zrangebyscore(key, start_time, end_time)

        metrics = []
        for item in raw_data:
            try:
                data = json.loads(item)
                metric = MetricData(timestamp=data["timestamp"], value=data["value"], tags=json.loads(data["tags"]))
                metrics.append(metric)
            except (json.JSONDecodeError, KeyError) as e:
                app_logger.warning(f"解析指标数据失败: {e}")

        return metrics


class AlertManager:
    """告警管理器"""

    def __init__(self, metrics_collector: MetricsCollector):
        self.metrics_collector = metrics_collector
        self.alert_rules = {}
        self.alert_states = defaultdict(dict)  # 告警状态
        self.alert_callbacks = []
        self._monitoring = False
        self._monitor_thread = None
        self._check_interval = 10  # 10秒检查一次

    def add_rule(self, rule: AlertRule):
        """添加告警规则"""
        self.alert_rules[rule.name] = rule
        app_logger.info(f"添加告警规则: {rule.name}")

    def remove_rule(self, rule_name: str):
        """移除告警规则"""
        if rule_name in self.alert_rules:
            del self.alert_rules[rule_name]
            app_logger.info(f"移除告警规则: {rule_name}")

    def add_callback(self, callback: Callable):
        """添加告警回调"""
        self.alert_callbacks.append(callback)

    def start_monitoring(self):
        """启动告警监控"""
        if self._monitoring:
            return

        self._monitoring = True

        def monitor_loop():
            app_logger.info("告警监控已启动")

            while self._monitoring:
                try:
                    self._check_alerts()
                except Exception as e:
                    app_logger.error(f"告警检查异常: {e}")

                time.sleep(self._check_interval)

        self._monitor_thread = threading.Thread(target=monitor_loop, daemon=True)
        self._monitor_thread.start()

    def stop_monitoring(self):
        """停止告警监控"""
        self._monitoring = False
        app_logger.info("告警监控已停止")

    def _check_alerts(self):
        """检查告警条件"""
        current_time = time.time()

        for rule_name, rule in self.alert_rules.items():
            if not rule.enabled:
                continue

            try:
                # 获取最近的指标数据
                metrics = self.metrics_collector.get_metrics(
                    rule.metric_name, start_time=current_time - rule.duration, end_time=current_time
                )

                if not metrics:
                    continue

                # 检查告警条件
                latest_value = metrics[-1].value
                is_triggered = self._evaluate_condition(latest_value, rule.condition, rule.threshold)

                # 更新告警状态
                alert_state = self.alert_states[rule_name]

                if is_triggered:
                    if "triggered_at" not in alert_state:
                        # 新触发的告警
                        alert_state["triggered_at"] = current_time
                        alert_state["notified"] = False

                    # 检查是否需要发送通知
                    if not alert_state["notified"] and current_time - alert_state["triggered_at"] >= rule.duration:
                        self._send_alert(rule, latest_value, metrics)
                        alert_state["notified"] = True
                else:
                    # 告警恢复
                    if "triggered_at" in alert_state:
                        if alert_state["notified"]:
                            self._send_recovery(rule, latest_value)
                        del alert_state["triggered_at"]
                        alert_state["notified"] = False

            except Exception as e:
                app_logger.error(f"检查告警规则失败 {rule_name}: {e}")

    def _evaluate_condition(self, value: float, condition: str, threshold: float) -> bool:
        """评估告警条件"""
        if condition == "gt":
            return value > threshold
        elif condition == "lt":
            return value < threshold
        elif condition == "gte":
            return value >= threshold
        elif condition == "lte":
            return value <= threshold
        elif condition == "eq":
            return value == threshold
        else:
            return False

    def _send_alert(self, rule: AlertRule, value: float, metrics: List[MetricData]):
        """发送告警通知"""
        alert_data = {
            "rule_name": rule.name,
            "metric_name": rule.metric_name,
            "current_value": value,
            "threshold": rule.threshold,
            "condition": rule.condition,
            "timestamp": time.time(),
            "metrics_count": len(metrics),
        }

        app_logger.warning(
            f"🚨 告警触发: {rule.name} - {rule.metric_name} {rule.condition} {rule.threshold}, 当前值: {value}"
        )

        # 调用规则回调
        if rule.callback:
            try:
                rule.callback(alert_data)
            except Exception as e:
                app_logger.error(f"告警回调执行失败: {e}")

        # 调用全局回调
        for callback in self.alert_callbacks:
            try:
                callback(alert_data)
            except Exception as e:
                app_logger.error(f"告警回调执行失败: {e}")

    def _send_recovery(self, rule: AlertRule, value: float):
        """发送恢复通知"""
        recovery_data = {
            "rule_name": rule.name,
            "metric_name": rule.metric_name,
            "current_value": value,
            "threshold": rule.threshold,
            "timestamp": time.time(),
        }

        app_logger.info(f"✅ 告警恢复: {rule.name} - 当前值: {value}")

        # 可以添加恢复通知的回调


class MonitoringSystem:
    """监控系统"""

    def __init__(self):
        self.metrics_collector = None
        self.alert_manager = None
        self._system_monitoring = False
        self._system_monitor_thread = None
        self._initialized = False

    def _ensure_initialized(self):
        """确保监控系统已初始化"""
        if not self._initialized:
            self.metrics_collector = MetricsCollector()
            self.alert_manager = AlertManager(self.metrics_collector)
            self._setup_default_alerts()
            self._initialized = True

    def _setup_default_alerts(self):
        """设置默认告警规则"""
        # 内存使用告警
        memory_alert = AlertRule(
            name="high_memory_usage",
            metric_name="memory_usage_mb",
            condition="gt",
            threshold=2048,  # 2GB
            duration=60,
        )
        self.alert_manager.add_rule(memory_alert)

        # 任务失败率告警
        task_failure_alert = AlertRule(
            name="high_task_failure_rate",
            metric_name="task_failure_rate",
            condition="gt",
            threshold=0.1,  # 10%
            duration=300,  # 5分钟
        )
        self.alert_manager.add_rule(task_failure_alert)

    def start_system_monitoring(self):
        """启动系统监控"""
        self._ensure_initialized()

        if self._system_monitoring:
            return

        self._system_monitoring = True
        self.alert_manager.start_monitoring()

        def system_monitor_loop():
            app_logger.info("系统监控已启动")

            while self._system_monitoring:
                try:
                    self._collect_system_metrics()
                except Exception as e:
                    app_logger.error(f"系统指标收集异常: {e}")

                time.sleep(30)  # 30秒收集一次

        self._system_monitor_thread = threading.Thread(target=system_monitor_loop, daemon=True)
        self._system_monitor_thread.start()

    def stop_system_monitoring(self):
        """停止系统监控"""
        self._system_monitoring = False
        self.alert_manager.stop_monitoring()
        app_logger.info("系统监控已停止")

    def _collect_system_metrics(self):
        """收集系统指标"""
        # 收集内存指标
        memory_usage = memory_manager.get_memory_usage()
        self.metrics_collector.record_metric("memory_usage_mb", memory_usage["rss_mb"])
        self.metrics_collector.record_metric("memory_percent", memory_usage["percent"])

        # 收集并发指标
        resource_stats = concurrency_manager.get_resource_stats()
        for resource_name, stats in resource_stats.items():
            self.metrics_collector.record_metric(
                f"resource_usage_{resource_name}", stats["current_count"], tags={"resource": resource_name}
            )

    def record_metric(self, name: str, value: float, tags: Dict[str, str] = None):
        """记录自定义指标"""
        # 只有在监控系统已启动时才记录指标，避免不必要的初始化
        if self._initialized and self.metrics_collector:
            self.metrics_collector.record_metric(name, value, tags)

    def get_metrics_summary(self, hours: int = 1) -> Dict[str, Any]:
        """获取指标摘要"""
        end_time = time.time()
        start_time = end_time - (hours * 3600)

        summary = {}

        # 获取内存指标
        memory_metrics = self.metrics_collector.get_metrics("memory_usage_mb", start_time, end_time)
        if memory_metrics:
            values = [m.value for m in memory_metrics]
            summary["memory"] = {
                "current": values[-1] if values else 0,
                "max": max(values) if values else 0,
                "min": min(values) if values else 0,
                "avg": sum(values) / len(values) if values else 0,
            }

        return summary


# 全局监控系统实例
monitoring_system = MonitoringSystem()


def start_monitoring():
    """启动监控系统"""
    monitoring_system.start_system_monitoring()


def stop_monitoring():
    """停止监控系统"""
    monitoring_system.stop_system_monitoring()


def record_metric(name: str, value: float, tags: Dict[str, str] = None):
    """记录指标"""
    monitoring_system.record_metric(name, value, tags)


# 程序退出时自动停止监控
import atexit

atexit.register(stop_monitoring)
