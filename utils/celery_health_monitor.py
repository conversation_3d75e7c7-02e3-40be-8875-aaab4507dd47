#!/usr/bin/env python3.11
"""
Celery 健康监控器 - 集成所有监控功能
提供统一的健康检查、自动恢复和预警机制
"""

import os
import subprocess
import threading
import time
from datetime import datetime
from typing import TYPE_CHECKING, Any, Dict, List, Optional

if TYPE_CHECKING:
    from celery_task.task_recovery import TaskRecovery

from logger.logger import app_logger
from utils.celery_manager import celery_manager, get_worker_status
from utils.memory_manager import memory_manager
from utils.redis_pool import get_redis_client


class CeleryHealthMonitor:
    """Celery 健康监控器 - 统一监控和自动恢复，避免多进程冲突"""

    def __init__(self):
        self.monitoring = False
        self.monitor_thread = None
        self.check_interval = 60  # 增加到60秒检查一次，减少频率
        self.failure_count = 0
        self.failure_threshold = 5  # 增加到5次失败才触发重启，避免频繁重启
        self.last_health_restart = 0
        self.health_restart_interval = 7200  # 2小时健康重启检查

        # 分层检查频率控制
        self.last_task_recovery_check = 0
        self.task_recovery_interval = 180  # 3分钟检查一次卡住的任务
        self.task_recovery_enabled = False  # 禁用任务恢复，依赖前端重新生成
        self.last_redis_check = 0
        self.redis_check_interval = 90  # 90秒检查一次 Redis 连接
        self.check_counter = 0  # 检查计数器，用于分层检查

        # 防止多进程冲突的锁文件
        self.lock_file = "/tmp/celery_health_monitor.lock"
        self.restart_lock_file = "/tmp/celery_restart.lock"

        # 重启频率控制
        self.last_restart_time = 0
        self.min_restart_interval = 300  # 最小重启间隔5分钟

        # 监控指标
        self.metrics = {
            "last_check_time": None,
            "health_score": 0,
            "consecutive_failures": 0,
            "total_restarts": 0,
            "last_restart_time": None,
            "memory_warnings": 0,
            "redis_connection_issues": 0,
            "task_recovery_count": 0,
        }

        # 任务恢复器 - 延迟初始化
        self.task_recovery: Optional["TaskRecovery"] = None

    def _ensure_task_recovery(self):
        """确保任务恢复器已初始化"""
        if self.task_recovery is None:
            from celery_task.task_recovery import TaskRecovery

            self.task_recovery = TaskRecovery()
        assert self.task_recovery is not None  # 帮助类型检查器

    def start_monitoring(self):
        """启动健康监控 - 带防冲突机制"""
        # 检查是否已有其他实例在运行
        if self._is_already_running():
            app_logger.warning("健康监控已在其他进程中运行，退出当前实例")
            return False

        if self.monitoring:
            app_logger.warning("健康监控已在运行")
            return True

        # 创建锁文件
        self._create_lock_file()

        self.monitoring = True
        self.monitor_thread = threading.Thread(target=self._monitor_loop, daemon=True)
        self.monitor_thread.start()
        app_logger.info("🚀 Celery 统一健康监控已启动")
        return True

    def stop_monitoring(self):
        """停止健康监控"""
        self.monitoring = False
        if self.monitor_thread:
            self.monitor_thread.join(timeout=5)
        app_logger.info("⏹️ Celery 健康监控已停止")

    def _monitor_loop(self):
        """监控主循环 - 集成所有监控功能"""
        while self.monitoring:
            try:
                # 更新锁文件时间戳
                self._update_lock_file()

                # 执行综合健康检查（集成原来的多个监控功能）
                self._perform_comprehensive_check()

                time.sleep(self.check_interval)
            except Exception as e:
                app_logger.error(f"健康监控异常: {e}")
                time.sleep(self.check_interval)
            finally:
                # 清理锁文件
                if not self.monitoring:
                    self._remove_lock_file()

    def _perform_health_check(self):
        """执行健康检查"""
        check_time = datetime.now()
        self.metrics["last_check_time"] = check_time

        app_logger.debug("🔍 执行 Celery 健康检查...")

        # 1. 检查 Worker 状态
        worker_health = self._check_worker_health()

        # 2. 检查内存使用
        memory_health = self._check_memory_health()

        # 3. 检查 Redis 连接
        redis_health = self._check_redis_health()

        # 4. 检查任务积压
        queue_health = self._check_queue_health()

        # 5. 检查卡住的任务
        stuck_tasks = self._check_stuck_tasks()

        # 计算综合健康分数
        health_score = self._calculate_health_score(worker_health, memory_health, redis_health, queue_health)

        self.metrics["health_score"] = health_score

        # 决定是否需要采取行动
        self._handle_health_issues(health_score, stuck_tasks)

        # 定期健康重启检查
        self._check_periodic_restart()

    def _perform_comprehensive_check(self):
        """执行综合健康检查 - 集成所有监控功能"""
        check_time = datetime.now()
        self.metrics["last_check_time"] = check_time

        app_logger.debug("🔍 执行 Celery 综合健康检查...")

        # 1. 检查 Worker 状态（原实时监控功能）
        worker_health = self._check_worker_health()

        # 2. 检查内存使用（原内存监控功能）
        memory_health = self._check_memory_health()

        # 3. 检查 Redis 连接
        redis_health = self._check_redis_health()

        # 4. 检查任务积压
        queue_health = self._check_queue_health()

        # 5. 检查卡住的任务（原 OOM 恢复功能）
        stuck_tasks = self._check_stuck_tasks()

        # 6. 检查 OOM 事件（原 OOM 恢复功能）
        self._check_oom_events()

        # 计算综合健康分数
        health_score = self._calculate_health_score(worker_health, memory_health, redis_health, queue_health)

        self.metrics["health_score"] = health_score

        # 决定是否需要采取行动
        self._handle_health_issues(health_score, stuck_tasks)

        # 定期健康重启检查
        self._check_periodic_restart()

    def _is_already_running(self) -> bool:
        """检查是否已有其他监控实例在运行"""
        try:
            if os.path.exists(self.lock_file):
                with open(self.lock_file, "r") as f:
                    pid_str = f.read().strip()
                    if pid_str.isdigit():
                        pid = int(pid_str)
                        # 检查进程是否还存在
                        try:
                            os.kill(pid, 0)
                            return True  # 进程存在
                        except OSError:
                            # 进程不存在，删除过期的锁文件
                            os.remove(self.lock_file)
                            return False
            return False
        except Exception as e:
            app_logger.error(f"检查锁文件失败: {e}")
            return False

    def _create_lock_file(self):
        """创建锁文件"""
        try:
            with open(self.lock_file, "w") as f:
                f.write(str(os.getpid()))
        except Exception as e:
            app_logger.error(f"创建锁文件失败: {e}")

    def _update_lock_file(self):
        """更新锁文件时间戳"""
        try:
            if os.path.exists(self.lock_file):
                os.utime(self.lock_file, None)
        except Exception as e:
            app_logger.error(f"更新锁文件失败: {e}")

    def _remove_lock_file(self):
        """移除锁文件"""
        try:
            if os.path.exists(self.lock_file):
                os.remove(self.lock_file)
        except Exception as e:
            app_logger.error(f"移除锁文件失败: {e}")

    def _check_oom_events(self):
        """检查 OOM 事件 - 集成原 OOM 恢复功能"""
        try:
            # 检查系统日志中的 OOM 事件
            result = subprocess.run(["dmesg"], capture_output=True, text=True, timeout=10)
            if "Out of memory" in result.stdout or "Killed process" in result.stdout:
                app_logger.warning("检测到 OOM 事件")

                # 只记录OOM事件，不自动恢复任务（依赖前端重新生成）
                if self.task_recovery_enabled:
                    # 查找并处理卡住的任务
                    self._ensure_task_recovery()
                    stuck_tasks = self.task_recovery.find_interrupted_tasks(max_age_minutes=5)  # OOM 情况下更短的超时
                    if stuck_tasks:
                        app_logger.warning(f"发现 {len(stuck_tasks)} 个可能因 OOM 中断的任务")
                        result = self.task_recovery.recover_all_interrupted_tasks(
                            max_age_minutes=5, max_recovery_count=3
                        )
                        self.metrics["task_recovery_count"] += result["recovered"]
                else:
                    app_logger.info("OOM 事件已记录，但自动任务恢复已禁用（依赖前端重新生成）")

        except Exception as e:
            app_logger.error(f"OOM 检查失败: {e}")

    def _check_worker_health(self) -> Dict[str, Any]:
        """检查 Worker 健康状态"""
        try:
            status = get_worker_status()
            return {
                "healthy": status.get("health_score", 0) >= 80,
                "score": status.get("health_score", 0),
                "processes": status.get("processes", {}),
                "celery_workers": status.get("celery_workers", {}),
                "connection_quality": status.get("connection_quality", {}),
            }
        except Exception as e:
            app_logger.error(f"Worker 健康检查失败: {e}")
            return {"healthy": False, "score": 0, "error": str(e)}

    def _check_memory_health(self) -> Dict[str, Any]:
        """检查内存健康状态"""
        try:
            memory_usage = memory_manager.get_memory_usage()

            # 检查是否超过警告阈值
            warning_threshold_mb = 1500  # 1.5GB
            critical_threshold_mb = 2000  # 2GB

            current_mb = memory_usage["rss_mb"]

            if current_mb > critical_threshold_mb:
                self.metrics["memory_warnings"] += 1
                return {
                    "healthy": False,
                    "level": "critical",
                    "usage_mb": current_mb,
                    "threshold_mb": critical_threshold_mb,
                }
            elif current_mb > warning_threshold_mb:
                return {
                    "healthy": True,
                    "level": "warning",
                    "usage_mb": current_mb,
                    "threshold_mb": warning_threshold_mb,
                }
            else:
                return {"healthy": True, "level": "normal", "usage_mb": current_mb}

        except Exception as e:
            app_logger.error(f"内存健康检查失败: {e}")
            return {"healthy": False, "error": str(e)}

    def _check_redis_health(self) -> Dict[str, Any]:
        """检查 Redis 连接健康状态"""
        try:
            redis_client = get_redis_client("health_check")

            start_time = time.time()
            redis_client.ping()
            response_time = time.time() - start_time

            if response_time > 2.0:
                self.metrics["redis_connection_issues"] += 1
                return {"healthy": False, "response_time": response_time, "issue": "slow_response"}
            elif response_time > 1.0:
                return {"healthy": True, "response_time": response_time, "warning": "slow_response"}
            else:
                return {"healthy": True, "response_time": response_time}

        except Exception as e:
            app_logger.error(f"Redis 健康检查失败: {e}")
            self.metrics["redis_connection_issues"] += 1
            return {"healthy": False, "error": str(e)}

    def _check_queue_health(self) -> Dict[str, Any]:
        """检查队列健康状态"""
        # 暂时简化队列检查，避免异步类型问题
        try:
            # 简单的连接测试
            redis_client = get_redis_client("queue_check")
            redis_client.ping()
            return {"healthy": True, "queue_length": "check_disabled", "note": "simplified_check"}
        except Exception as e:
            app_logger.error(f"队列健康检查失败: {e}")
            return {"healthy": False, "error": str(e)}

    def _check_stuck_tasks(self) -> List[Dict[str, Any]]:
        """检查卡住的任务 - 控制检查频率"""
        # 任务恢复已禁用，依赖前端重新生成机制
        if not self.task_recovery_enabled:
            return []

        current_time = time.time()

        # 只有距离上次检查超过指定间隔才执行
        if current_time - self.last_task_recovery_check < self.task_recovery_interval:
            return []

        try:
            self.last_task_recovery_check = current_time
            self._ensure_task_recovery()
            stuck_tasks = self.task_recovery.find_interrupted_tasks(max_age_minutes=10)
            if stuck_tasks:
                app_logger.info(f"发现 {len(stuck_tasks)} 个卡住的任务")
            return stuck_tasks
        except Exception as e:
            app_logger.error(f"卡住任务检查失败: {e}")
            return []

    def _calculate_health_score(self, worker_health, memory_health, redis_health, queue_health) -> int:
        """计算综合健康分数"""
        score = 100

        # Worker 健康状态 (40%)
        if not worker_health.get("healthy", False):
            score -= 40
        else:
            worker_score = worker_health.get("score", 100)
            score = score * (worker_score / 100)

        # 内存健康状态 (25%)
        if not memory_health.get("healthy", False):
            if memory_health.get("level") == "critical":
                score -= 25
            else:
                score -= 10

        # Redis 健康状态 (20%)
        if not redis_health.get("healthy", False):
            score -= 20
        elif redis_health.get("warning"):
            score -= 5

        # 队列健康状态 (15%)
        if not queue_health.get("healthy", False):
            score -= 15
        elif queue_health.get("warning"):
            score -= 5

        return max(0, int(score))

    def _handle_health_issues(self, health_score: int, stuck_tasks: List):
        """处理健康问题"""
        # 处理卡住的任务 - 已禁用自动恢复
        if stuck_tasks and self.task_recovery_enabled:
            app_logger.warning(f"发现 {len(stuck_tasks)} 个卡住的任务，开始恢复...")
            try:
                self._ensure_task_recovery()
                result = self.task_recovery.recover_all_interrupted_tasks(max_age_minutes=10, max_recovery_count=5)
                self.metrics["task_recovery_count"] += result["recovered"]
                app_logger.info(f"任务恢复完成: 恢复 {result['recovered']} 个任务")
            except Exception as e:
                app_logger.error(f"任务恢复失败: {e}")
        elif stuck_tasks:
            app_logger.info(f"发现 {len(stuck_tasks)} 个卡住的任务，但自动恢复已禁用（依赖前端重新生成）")

        # 简化健康检查逻辑 - 获取Worker健康状态
        try:
            from utils.celery_manager import celery_manager

            health_status = celery_manager.check_worker_health()

            if not health_status.get("is_healthy", True):
                self.failure_count += 1
                issues = health_status.get("issues", [])
                app_logger.warning(f"Celery Worker不健康 (连续失败 {self.failure_count} 次): {', '.join(issues)}")

                if self.failure_count >= self.failure_threshold:
                    app_logger.error(f"连续 {self.failure_count} 次健康检查失败，触发Circus重启")
                    self._trigger_circus_restart("worker_unhealthy")
            else:
                # 重置失败计数
                if self.failure_count > 0:
                    app_logger.info("健康状态恢复正常")
                    self.failure_count = 0

        except Exception as e:
            app_logger.error(f"Worker健康检查失败: {e}")
            # 如果健康检查本身失败，也认为是不健康的
            self.failure_count += 1
            if self.failure_count >= self.failure_threshold:
                app_logger.error(f"连续 {self.failure_count} 次健康检查异常，触发Circus重启")
                self._trigger_circus_restart("health_check_failed")

    def _check_periodic_restart(self):
        """检查是否需要定期健康重启"""
        current_time = time.time()

        if (current_time - self.last_health_restart) > self.health_restart_interval:
            # 检查是否需要健康重启
            try:
                status = get_worker_status()
                processes = status.get("processes", {}).get("details", [])

                if processes:
                    proc = processes[0]
                    uptime_hours = proc.get("running_time", 0) / 3600
                    memory_mb = proc.get("memory_mb", 0)

                    # 如果运行超过24小时或内存增长过多，考虑健康重启
                    if uptime_hours > 24 or memory_mb > 1024:
                        app_logger.info(f"触发定期健康重启: 运行时间 {uptime_hours:.1f}h, 内存 {memory_mb:.1f}MB")
                        self._trigger_restart("periodic_health")

            except Exception as e:
                app_logger.error(f"定期重启检查失败: {e}")

            self.last_health_restart = current_time

    def _trigger_circus_restart(self, reason: str):
        """触发Circus重启 - 带防冲突机制和频率控制"""
        # 检查重启间隔
        current_time = time.time()
        if current_time - self.last_restart_time < self.min_restart_interval:
            remaining_time = self.min_restart_interval - (current_time - self.last_restart_time)
            app_logger.warning(f"重启间隔不足，跳过重启请求: {reason}，还需等待 {remaining_time:.0f} 秒")
            return

        # 检查是否已有重启在进行
        if self._is_restart_in_progress():
            app_logger.warning(f"重启已在进行中，跳过本次重启请求: {reason}")
            return

        app_logger.warning(f"🔄 触发 Circus Worker 重启，原因: {reason}")
        self.last_restart_time = current_time

        try:
            # 创建重启锁
            self._create_restart_lock()

            # 使用Circus重启（推荐方式）
            success = celery_manager.request_circus_restart()

            if success:
                app_logger.info("✅ Circus Worker 重启成功")
                self.metrics["total_restarts"] += 1
                self.metrics["last_restart_time"] = datetime.now()
                self.failure_count = 0
            else:
                app_logger.error("❌ Circus Worker 重启失败")

        except Exception as e:
            app_logger.error(f"重启过程中发生异常: {e}")
        finally:
            # 移除重启锁
            self._remove_restart_lock()

    def _trigger_restart(self, reason: str):
        """触发重启 - 兼容性方法，重定向到Circus重启"""
        app_logger.warning(f"使用兼容性重启方法，重定向到Circus重启: {reason}")
        self._trigger_circus_restart(reason)

    def _is_restart_in_progress(self) -> bool:
        """检查是否有重启在进行"""
        try:
            if os.path.exists(self.restart_lock_file):
                # 检查锁文件是否过期（超过5分钟认为过期）
                lock_time = os.path.getmtime(self.restart_lock_file)
                if time.time() - lock_time > 300:  # 5分钟
                    os.remove(self.restart_lock_file)
                    return False
                return True
            return False
        except Exception as e:
            app_logger.error(f"检查重启锁失败: {e}")
            return False

    def _create_restart_lock(self):
        """创建重启锁"""
        try:
            with open(self.restart_lock_file, "w") as f:
                f.write(str(os.getpid()))
        except Exception as e:
            app_logger.error(f"创建重启锁失败: {e}")

    def _remove_restart_lock(self):
        """移除重启锁"""
        try:
            if os.path.exists(self.restart_lock_file):
                os.remove(self.restart_lock_file)
        except Exception as e:
            app_logger.error(f"移除重启锁失败: {e}")

    def get_health_report(self) -> Dict[str, Any]:
        """获取健康报告"""
        return {
            "monitoring_active": self.monitoring,
            "last_check": self.metrics["last_check_time"].isoformat() if self.metrics["last_check_time"] else None,
            "health_score": self.metrics["health_score"],
            "consecutive_failures": self.failure_count,
            "metrics": self.metrics.copy(),
        }


# 全局健康监控器实例
health_monitor = CeleryHealthMonitor()


def start_health_monitoring():
    """启动健康监控并保持运行"""
    app_logger.info("启动 Celery 健康监控服务...")

    if not health_monitor.start_monitoring():
        app_logger.error("健康监控启动失败")
        return

    app_logger.info("健康监控服务已启动，开始监控循环...")

    try:
        # 保持主进程运行，避免守护线程退出
        while health_monitor.monitoring:
            time.sleep(60)
            # 可选：定期输出健康报告
            try:
                report = health_monitor.get_health_report()
                app_logger.debug(f"健康分数: {report['health_score']}/100")
            except Exception as e:
                app_logger.warning(f"获取健康报告失败: {e}")

    except KeyboardInterrupt:
        app_logger.info("收到中断信号，停止健康监控...")
    except Exception as e:
        app_logger.error(f"健康监控运行异常: {e}")
    finally:
        health_monitor.stop_monitoring()
        app_logger.info("健康监控服务已停止")


def stop_health_monitoring():
    """停止健康监控"""
    health_monitor.stop_monitoring()


def get_health_status() -> Dict[str, Any]:
    """获取健康状态"""
    return health_monitor.get_health_report()


if __name__ == "__main__":
    # 作为独立脚本运行
    import signal
    import sys

    def signal_handler(sig, frame):
        _ = sig, frame  # 避免未使用参数警告
        print("\n正在停止健康监控...")
        health_monitor.stop_monitoring()
        sys.exit(0)

    signal.signal(signal.SIGINT, signal_handler)
    signal.signal(signal.SIGTERM, signal_handler)

    print("启动 Celery 健康监控...")
    health_monitor.start_monitoring()

    try:
        while True:
            time.sleep(60)
            report = health_monitor.get_health_report()
            print(f"健康分数: {report['health_score']}/100")
    except KeyboardInterrupt:
        pass
