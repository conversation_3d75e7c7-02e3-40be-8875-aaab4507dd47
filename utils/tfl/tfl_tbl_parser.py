# -*- coding: utf-8 -*-
"""
RTF 表格提取器
功能1：RTF → DOCX → 提取表格（标题、内容、缩进、合并单元格、脚注）
功能2：RTF → DOCX → 提取OOXML
"""
import os
import subprocess

from docx import Document
from docx.oxml.ns import nsmap
from docx.oxml import OxmlElement
from docx.oxml.ns import qn
import zipfile
import xml.etree.ElementTree as ET
from datetime import datetime
import re

from logger.logger import app_logger

soffice_path = "soffice"
# soffice_path = "/Applications/LibreOffice.app/Contents/MacOS/soffice"
resource_base_path = 'static/tfl'

def remove_rtf_keywords(input_file, output_file, keywords):
    # 读取 RTF 文件
    with open(input_file, 'r', encoding='utf-8') as f:
        content = f.read()

    # 对每个关键字构造正则表达式：匹配 \keyword 后面不能是字母（即完整控制词）
    for keyword in keywords:
        # 正则：匹配 \keyword 且后面不是字母（即完整词），可选空格或控制符
        pattern = r'\\' + re.escape(keyword) + r'(?![a-zA-Z])'
        content = re.sub(pattern, '', content)

    # 保存处理后的 RTF
    with open(output_file, 'w', encoding='utf-8') as f:
        f.write(content)

    print(f"处理完成，已保存到 {output_file}")

def rtf_to_docx(rtf_path, output_dir=None):
    """
    使用 LibreOffice 将 RTF 转为 DOCX
    """
    if not os.path.exists(rtf_path):
        raise FileNotFoundError(f"RTF 文件不存在: {rtf_path}")

    if output_dir is None:
        output_dir = os.path.dirname(rtf_path) or "."
    os.makedirs(output_dir, exist_ok=True)

    try:
        # result = subprocess.run(
        #     [
        #         soffice_path,
        #         "--headless",
        #         "--convert-to", "docx",
        #         "--outdir", output_dir,
        #         rtf_path
        #     ],
        #     capture_output=True,
        #     text=True,
        #     encoding='utf-8',
        #     errors='ignore'
        # )
        # if result.returncode != 0:
        #     raise RuntimeError(f"转换失败: {result.stderr}, {result.stdout}")

        base_name = os.path.splitext(os.path.basename(rtf_path))[0]
        docx_path = os.path.join(output_dir, f"{base_name}.docx")
        subprocess.run(['unoconv', '-f', 'docx', '-o', docx_path, rtf_path])

        if not os.path.exists(docx_path):
            raise RuntimeError(f"转换成功但未生成 DOCX 文件, docx_path={docx_path}, rtf_path={rtf_path}")
        print(f"RTF 已转换为: docx_path={docx_path}, rtf_path={rtf_path}")
        return docx_path

    except FileNotFoundError:
        raise RuntimeError("未找到 soffice。请安装 LibreOffice 并加入系统 PATH。")

def get_element_type(element):
    """获取元素类型：'paragraph', 'table', 'page_break', 'other'"""
    tag = element.tag.split('}')[-1]
    # print(f"tag={tag}")
    if tag == 'p':
        return 'paragraph'
    elif tag == 'tbl':
        return 'table'
    elif tag == 'br':
        # 检查是否为分页符
        br = OxmlElement('w:br')
        br.set(qn('w:type'), 'page')
        if hasattr(element, 'xml') and element.xml == br.xml:
            return 'page_break'
    return 'other'

def get_indent_from_xml(paragraph):
    """
    直接从段落的 XML 中提取 w:start (左缩进) 的值。
    这是当 python-docx 的 paragraph_format.left_indent 失效时的备用方案。

    :param paragraph: docx.text.Paragraph 对象
    :return: 左缩进值（Twip），如果未设置则返回 0
    """
    # 获取段落的 XML 元素
    p_element = paragraph._p

    # 查找 <w:pPr> 元素
    pPr = p_element.find("./w:pPr", namespaces=nsmap)
    if pPr is None:
        return 0

    # 在 <w:pPr> 中查找 <w:ind> 元素
    ind = pPr.find("./w:ind", namespaces=nsmap)
    if ind is None:
        return 0

    # 获取 w:start 属性
    start_str = ind.get("{http://schemas.openxmlformats.org/wordprocessingml/2006/main}start")
    if start_str is None:
        return 0

    try:
        return int(start_str)
    except ValueError:
        return 0

def get_indent_level(paragraph, tolerance_twip=360000):
    """
    获取段落的缩进级别，优先使用底层 XML。

    :param paragraph: docx.text.Paragraph 对象
    :param tolerance_twip: 将物理缩进（Twip）映射为逻辑级别的容差值。默认 360000 Twip (~0.25英寸)。
    :return: 缩进级别 (int)
    """
    # 首选：尝试从底层 XML 读取 w:start
    start_indent_twip = get_indent_from_xml(paragraph)
    if start_indent_twip > 0:
        return int(start_indent_twip / tolerance_twip)

    # 回退：如果 XML 方法返回 0，再尝试使用 python-docx 的标准方法
    try:
        left_indent = paragraph.paragraph_format.left_indent
        if left_indent is not None:
            # python-docx 使用 EMU, 1 Twip = 635 EMU
            left_indent_twip = int(left_indent / 635)
            return int(left_indent_twip / tolerance_twip)
    except:
        pass

    return 0

def has_cell_border(cell, border_type):
    """
    检查单元格是否有指定类型的边框。

    Args:
        cell: python-docx 的 Cell 对象。
        border_type (str): 'top', 'left', 'bottom', 'right' 之一。

    Returns:
        bool: 如果边框存在则返回 True，否则返回 False。
    """
    # 获取单元格的底层 XML 元素
    tc = cell._tc
    tcPr = tc.get_or_add_tcPr()

    # 使用 qn() 函数创建带命名空间的标签名
    # 例如: qn('w:tcBorders') 会返回 '{http://schemas.openxmlformats.org/wordprocessingml/2006/main}tcBorders'
    tc_borders_tag = qn('w:tcBorders')
    border_tag = qn(f'w:{border_type}')

    # 查找 <w:tcBorders> 元素
    tcBorders = tcPr.find(tc_borders_tag)
    if tcBorders is None:
        return False  # 没有边框设置

    # 在 <w:tcBorders> 中查找指定的边框元素，如 <w:top>
    border_element = tcBorders.find(border_tag)
    return border_element is not None

def extract_table_data(docx_path):
    """
    从 .docx 文件中提取第一个表格及其上下文（标题、脚注）。
    """
    doc = Document(docx_path)
    elements = list(doc.element.body)

    table_data = None
    tbl_datas = []
    tbl_texts = []
    tbl_idx = 0
    col_count = 0
    tbl_count = len(doc.element.body.findall('.//{http://schemas.openxmlformats.org/wordprocessingml/2006/main}tbl'))


    for elem in elements:
        elem_type = get_element_type(elem)

        # === 1. 查找表格 ===
        if elem_type == 'table': ##  and not found_table
            # 使用构造函数从 element 创建 Table 对象

            table = doc.tables[tbl_idx] if doc.tables else None
            if table is None:
                continue
            tbl_idx = tbl_idx + 1

            # 提取表格数据
            border_rows = []
            table_data = []
            table_texts = []
            row_idx = 0
            for row in table.rows:
                row_idx = row_idx + 1
                need_count_col = ((col_count == 0 and tbl_count == 1 and row_idx == 1)
                                  or (tbl_count > 1 and tbl_idx > 1 and col_count == 0 and row_idx == 1))
                row_texts = []
                row_data = []
                last_grid_span = row.cells[0].grid_span
                last_grid_span_text = row.cells[0].text
                for cell in row.cells:

                    row_texts.append(cell.text)
                    # print(f"cell={cell.text}, cell.grid_span={cell.grid_span}")
                    cell_text = cell.text #.strip()
                    has_top = has_cell_border(cell, 'top')
                    has_bottom = has_cell_border(cell, 'bottom')
                    # print(f"行 0, 单元格 {cell_text}: 上边框={has_top}, 下边框={has_bottom}")
                    # if has_top:
                    #     print('has top')
                    #     if need_count_col:
                    #         col_count += cell.grid_span
                    # if has_bottom:
                    #     print('has bottom')
                    #
                    # if has_top == False and has_bottom == False:
                    #     print('no border')
                    para = cell.paragraphs[0]

                    indent_level = get_indent_level(para)
                    # print(f"段落文本: '{para.text}', indent_level={indent_level}")

                    # 调试：打印原始的 w:start 值
                    raw_indent = get_indent_from_xml(para)

                    if raw_indent > 0:
                        cell_text = '    ' + cell_text
                        # print(f"{cell_text}: raw_indent={raw_indent}, indent_level={indent_level}")

                    # 清理多余空格和换行
                    # cell_text = ' '.join(cell_text.split())
                    if cell.grid_span == 1:
                        row_data.append({
                            'text': cell_text if cell_text else '',
                            'col_span': cell.grid_span,
                             'has_bottom': has_bottom,
                            'has_top': has_top
                         }) # 默认放入空格
                    elif cell.grid_span > 1 and last_grid_span > 1 and cell_text == last_grid_span_text:
                        last_grid_span = last_grid_span - 1
                        row_data.append({
                            'text': '',
                            'col_span': cell.grid_span,
                            'has_bottom': has_bottom,
                            'has_top': has_top
                        })
                    elif cell.grid_span > 1 and last_grid_span == 1 and cell_text == last_grid_span_text:
                        row_data.append({
                            'text': cell_text if cell_text else '',
                            'col_span': cell.grid_span,
                             'has_bottom': has_bottom,
                            'has_top': has_top
                         })
                    elif cell.grid_span > 1 and cell_text != last_grid_span_text:
                        last_grid_span = cell.grid_span - 1
                        last_grid_span_text = cell_text

                table_data.append(row_data)
                table_texts.append(row_texts)
            tbl_datas.append(table_data)
            tbl_texts.append(table_texts)

    return {
        "table": table_data or [],
        "tables": tbl_datas or [],
        "table_texts": tbl_texts or [],
        "col_count": col_count
    }


def setup_namespace_prefixes():
    """注册 OOXML 常用前缀，避免 ns0, ns1"""
    namespaces = {
        'w':  'http://schemas.openxmlformats.org/wordprocessingml/2006/main',
        'r':  'http://schemas.openxmlformats.org/officeDocument/2006/relationships',
        'wp': 'http://schemas.openxmlformats.org/drawingml/2006/wordprocessingDrawing',
        'a':  'http://schemas.openxmlformats.org/drawingml/2006/main',
        'mc': 'http://schemas.openxmlformats.org/markup-compatibility/2006',
        'w14': 'http://schemas.microsoft.com/office/word/2010/wordml',
        'w15': 'http://schemas.microsoft.com/office/word/2012/wordml',
        'o': 'urn:schemas-microsoft-com:office:office',
        'v': 'urn:schemas-microsoft-com:vml',
        'w10': 'urn:schemas-microsoft-com:office:word',
        'pic': "http://schemas.openxmlformats.org/drawingml/2006/picture",
        'wps': "http://schemas.microsoft.com/office/word/2010/wordprocessingShape",
        'wpg': "http://schemas.microsoft.com/office/word/2010/wordprocessingGroup",
        'wp14': "http://schemas.microsoft.com/office/word/2010/wordprocessingDrawing"
    }
    for prefix, uri in namespaces.items():
        ET.register_namespace(prefix, uri)
    return namespaces

def extract_table_ooxml(docx_path, from_rtf):
    """解析table tfl的OOXML
    :param docx_path: docx文件路径
    :param from_rtf: 是否来自RTF
    :return: ooxml字符串
    """
    filename = os.path.basename(docx_path)
    OOXML_TEMPLATE_FILE_PATH = f'{resource_base_path}/tfl-template.xml'
    # OOXML_TEMPLATE_RTF_FILE_PATH = f'{resource_base_path}/tfl-template_rtf.xml'
    tpl_path = OOXML_TEMPLATE_FILE_PATH
    
    ooxml_output_file_path = f'{resource_base_path}/output/{filename}_{datetime.now().strftime("%Y%m%d%H%M%S")}.xml'

    print(ooxml_output_file_path)
    with zipfile.ZipFile(docx_path, 'r') as docx_zip:
        # 1. 找到正文文档
        document_xml = docx_zip.read('word/document.xml')
        xml_string = document_xml.decode('utf-8')

        # 2. 设置所有支持的XML 协议
        NS = setup_namespace_prefixes()

        # 3. 初始化XML tree
        root = ET.fromstring(xml_string)

        # 4. 找出要删除的header（页眉）和footer（页脚）
        sect_pr_list = root.findall('.//w:sectPr', namespaces=NS)
        for sect_pr in sect_pr_list:
            to_remove = []
            for child in sect_pr:
                # 检查标签名是否为 headerReference 或 footerReference
                if child.tag == f'{{{NS["w"]}}}headerReference' or child.tag == f'{{{NS["w"]}}}footerReference':
                    to_remove.append(child)
            # 删除这些节点
            for node in to_remove:
                sect_pr.remove(node)

        body = root.find('.//w:body', namespaces=NS)
        # if not from_rtf:
        #     # 5. 找出要返回的body
        #     # 构造段落 XML
        #     p = ET.Element("{%s}p" % NS["w"])
        #
        #     # pPr
        #     pPr = ET.SubElement(p, "{%s}pPr" % NS["w"])
        #
        #     # 缩进
        #     ind = ET.SubElement(pPr, "{%s}ind" % NS["w"])
        #     ind.set("{%s}left" % NS["w"], "1152")
        #
        #     # 居中
        #     jc = ET.SubElement(pPr, "{%s}jc" % NS["w"])
        #     jc.set("{%s}val" % NS["w"], "center")
        #
        #     # 与下段同页
        #     keepNext = ET.SubElement(pPr, "{%s}keepNext" % NS["w"])
        #
        #     # 插入到 body 开头
        #     body.insert(0, p)

        # 6. 返回字符串（不带 XML 声明）
        clean_xml = ET.tostring(body, encoding='unicode', xml_declaration=False)

    if not clean_xml:
        return "", ""

    # 7. 定位要输出的模版ooxml文件
    with open(tpl_path, 'r', encoding='utf-8') as f:
        template_content = f.read()

    # 8. 替换占位符
    if '{DOCUMENT_XML}' not in template_content:
        raise ValueError(f"模板文件中未找到占位符 {{DOCUMENT_XML}}：{tpl_path}")
    ooxml_text = template_content.replace('{DOCUMENT_XML}', clean_xml)

    # 9. 确保输出目录存在
    os.makedirs(os.path.dirname(ooxml_output_file_path), exist_ok=True)

    # 10. 保存新文件
    with open(ooxml_output_file_path, 'w', encoding='utf-8') as f:
        f.write(ooxml_text)

    app_logger.info(f"rtf 对应的ooxml文件已保存：{ooxml_output_file_path}")

    return ooxml_output_file_path,ooxml_text

def process_rtf(tfl_file_path: str):
    # filename = os.path.basename(tfl_file_path)
    # 1. 判断文件类型：rtf/docx
    root, extension = os.path.splitext(tfl_file_path)

    print(root)
    print(extension)

    if extension[1:] == 'rtf':
        output_rtf = f'{root}-remove-keepn.rtf'
        keywords_to_remove = ['trkeep', 'keepn']
        remove_rtf_keywords(tfl_file_path, output_rtf, keywords_to_remove)
        # 2. 如果是rtf，使用libreoffice转化成docx
        docx_path = rtf_to_docx(output_rtf)
        from_rtf = True
    else:
        docx_path = tfl_file_path
        from_rtf = False
    app_logger.info(f"RTF to Docx转化成功：{docx_path}")

    # 3. 解析docx中的ooxml
    ooxml_file_path, ooxml_text = extract_table_ooxml(docx_path, from_rtf)

    # 4. 解析table的标题、脚注、cells
    result = extract_table_data(docx_path)
    caption_row = []
    footnote_row = []
    table_count = len(result["tables"])
    if table_count == 1: # 较正常的tfl文档，即全docx内只有一个tbl
        table = result["tables"][0]
        table_texts = result["table_texts"][0]
        cells = []
        for i, row in enumerate(table):
            if i == 0:
                caption_row = row
            elif i == len(table) -1:
                footnote_row = row
            else:
                cells.append(row)
        body_table = cells
    elif table_count == 2: # caption，cells 在2个不同的tbl中，常见于雅信诚的rtf
        caption_table = result["tables"][0]
        body_table = result["tables"][1]
        table_texts = result["table_texts"][1]
        caption_row = caption_table[0]
        footnote_row = []
        for i, row in enumerate(body_table):
            print(f"  第{i + 1}行: {row}")
    elif table_count == 3: # caption，cells，footnote 在3个不同的tbl中，常见于雅信诚的rtf
        caption_table = result["tables"][0]
        caption_row = caption_table[0]
        body_table = result["tables"][1]
        table_texts = result["table_texts"][1]
        footnote_table = result["tables"][2]
        footnote_row = footnote_table[0]
    elif table_count > 3:  # doc内有超过三个表，一般见于每一页都是一个独立的表的场景
        caption_table = result["tables"][0]
        caption_row = caption_table[0]
        body_table = result["tables"][1]
        table_texts = result["table_texts"][1]
        footnote_table = result["tables"][-1]
        footnote_row = footnote_table[-1]
    elif table_count == 0:
        body_table = []
        caption_row = []
        footnote_row = []
        table_texts = []

    caption = ''
    footnote = ''
    col_span = result.get('col_count', 0)
    for item in caption_row:
        caption += item.get('text')
    for item in footnote_row:
        footnote += item.get('text')

    return {
        "caption": caption,
        "footnote": footnote,
        "col_span": col_span,
        "cells": body_table or [],
        "table_texts": table_texts or [],
        "ooxml_file_path": ooxml_file_path,
        "ooxml_text": ooxml_text
    }


if __name__ == "__main__":
#     # rtf_file_path = "../static/tfl/oss-local-246-1752581953000-078503.rtf"  # 正常, has indent
#     # rtf_file_path = "../../static/tfl/tfl/t14_3_2_10.rtf"  # 正常, has indent
#     # rtf_file_path = "../../static/tfl/tfl/t14_3_4_6.rtf"  # 正常， double layer header
#     # rtf_file_path = "../static/tfl/t_ae_sum_SA.rtf"  # 正常
#     # rtf_file_path = "../static/tfl/t14_1_3.rtf"  # 正常
#     # rtf_file_path = "../static/tfl/t14_1_1_2_test.rtf"  # 正常
#     # rtf_file_path = "../static/tfl/f14_2_2_1.rtf"  # picture
#     # rtf_file_path = "../static/tfl/f14_2_5_1.rtf"  # picture, 没有相关数据。
#     # rtf_file_path = "../../static/tfl/tfl/YXC009_T140105.rtf"  # 雅信诚
#      rtf_file_path = "../../static/tfl/tfl/t_adae_freq_ctc.docx"  # 复宏汉霖
#      rtf_file_path = "../../static/tfl/tfl/t_adae_freq_ctc_docx.docx"  # 复宏汉霖
#     # rtf_file_path = "../../static/tfl/tfl/t_adae_freq_ctc_docx_border.docx"  # 复宏汉霖
#     # rtf_file_path = "../../static/tfl/tfl/fyz/t14_4_1.rtf"  # 复兴，多页，每一页是一个独立的table
#     rtf_file_path = "../../static/tfl/tfl/fyz/t14_4_3.rtf"  # 复兴，多页，每一页是一个独立的table
#     rtf_file_path = "../../static/tfl/tfl/fyz/oss-dev-20-1745854578000-723150.rtf"  # 复兴，多页，每一页是一个独立的table
#     rtf_file_path = "../../static/tfl/tfl/fyz/oss-dev-20-1745854747000-668809.rtf"  # 复兴，多页，每一页是一个独立的table
#     rtf_file_path = "../../static/tfl/tfl/t_adae_freq_ctc_docx_border.docx"  # 复兴，多页，每一页是一个独立的table
#     rtf_file_path = "../../static/tfl/tfl/t_adae_freq_ctc.rtf"  # 复兴，多页，每一页是一个独立的table
    rtf_file_path = "../../static/tfl/tfl/t_adpr.rtf"  # 复兴，多页，每一页是一个独立的table
    print(process_rtf(rtf_file_path))


# 模拟并发 API 请求
# if __name__ == "__main__":
#     files = ["../../static/tfl/tfl/fyz/t14_4_1.rtf",
#         "../../static/tfl/tfl/fyz/t14_4_3.rtf",  # 复兴，多页，每一页是一个独立的table
#         "../../static/tfl/tfl/fyz/oss-dev-20-1745854578000-723150.rtf"]  # 复兴，多页，每一页是一个独立的table
    # files = [f"doc{i}.odt" for i in range(5)]

    # threads = []
    # for f in files:
    #     t = threading.Thread(target=handle_conversion_task, args=(f,))
    #     t.start()
    #     threads.append(t)
    #
    # for t in threads:
    #     t.join()
    #
    # # 关闭池
    # LibreOfficePool().shutdown()

