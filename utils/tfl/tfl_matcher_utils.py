import copy
import re

import jieba
import numpy as np
from rank_bm25 import BM25<PERSON>ka<PERSON>

from logger.logger import app_logger


def custom_tokenize(text: str) -> list[str]:
    """
    自定义分词策略，按照要求过滤文本
    1. 去掉句子结尾的中英文括号包裹的纯英文大写
    2. 去掉*******这样的多级标题
    3. 去掉"FAS 集"、"SS 集"
    4. 去掉"-"
    5. 去掉所有的标点符号
    """
    text = re.sub(r"[（(][A-Z][A-Z\s]*[A-Z][）)]$", "", text)
    text = re.sub(r"[（(][A-Z][）)]$", "", text)
    text = re.sub(r"^\d+(\.\d+)*\s*", "", text)
    text = re.sub(r"\d+(\.\d+)+", "", text)
    text = re.sub(r"[A-Z]+\s*集", "", text)
    text = re.sub(r"-", "", text)
    text = re.sub(r"[^\u4e00-\u9fff\w\s]", "", text)
    text = re.sub(r"\s+", " ", text).strip()

    if not text:
        return list(jieba.cut(text))

    tokens = list(jieba.cut(text))
    filtered_tokens = [token.strip() for token in tokens if token.strip() and len(token.strip()) > 0]
    return filtered_tokens


def match_tfls(
    in_text_tfls: list[dict],
    tfls: list[dict],
    threshold: float = 0.3,
    allow_multiple_matches: bool = True,
    relative_threshold_ratio: float = 0.4,
    debug: bool = False,
) -> list[dict]:
    if not in_text_tfls or not tfls:
        raise ValueError("输入列表不能为空")

    in_names = [item["inTextName"] for item in in_text_tfls]
    tfl_names = [tfl["name"] for tfl in tfls]

    tokenized_tfls = [custom_tokenize(name) for name in tfl_names]
    bm25 = BM25Okapi(tokenized_tfls)

    app_logger.info(f"BM25模型初始化完成: {len(in_names)} inTexts, {len(tfl_names)} TFLs")

    similarity_matrix = _calculate_similarity_matrix(in_names, bm25, tfl_names, debug)

    result = copy.deepcopy(in_text_tfls)
    for entry in result:
        entry["tfls"] = []

    used_tfl_indices = set()

    _first_round_matching(result, tfls, similarity_matrix, used_tfl_indices, debug)

    if allow_multiple_matches:
        _second_round_matching_relative(
            result, tfls, similarity_matrix, used_tfl_indices, threshold, relative_threshold_ratio, debug
        )

    _log_matching_results(result)
    return result


def _calculate_similarity_matrix(
    in_names: list[str], bm25: BM25Okapi, tfl_names: list[str], debug: bool = False
) -> np.ndarray:
    similarity_matrix = []

    for i, in_name in enumerate(in_names):
        query_tokens = custom_tokenize(in_name)
        scores = bm25.get_scores(query_tokens)
        similarity_matrix.append(scores)

        if debug:
            app_logger.debug(f"\nDEBUG: 查询 '{in_name}' -> 清理后分词: {query_tokens}")
            app_logger.debug("  清理后的TFL对照:")
            for j, tfl_name in enumerate(tfl_names):
                cleaned_tfl = " ".join(custom_tokenize(tfl_name))
                app_logger.debug(f"    {j}: {tfl_name} -> {cleaned_tfl}")

            app_logger.debug("  Top5匹配:")
            top_indices = np.argsort(scores)[-5:][::-1]
            for idx in top_indices:
                app_logger.debug(f"    {tfl_names[idx]} -> {scores[idx]:.4f}")

    return np.array(similarity_matrix)


def _first_round_matching(
    result: list[dict], tfls: list[dict], similarity_matrix: np.ndarray, used_tfl_indices: set, debug: bool = False
):
    if debug:
        app_logger.debug("\n=== 第一轮匹配开始 ===")

    candidates = [(i, j, similarity_matrix[i][j]) for i in range(len(result)) for j in range(len(tfls))]
    candidates.sort(key=lambda x: x[2], reverse=True)

    matched_intexts = set()

    for in_idx, tfl_idx, score in candidates:
        if in_idx not in matched_intexts and tfl_idx not in used_tfl_indices:
            result[in_idx]["tfls"].append(tfls[tfl_idx])
            used_tfl_indices.add(tfl_idx)
            matched_intexts.add(in_idx)

            if debug:
                app_logger.debug(
                    f"  第一轮匹配: {result[in_idx]['inTextName']} -> {tfls[tfl_idx]['name']} (score: {score:.4f})"
                )


def _second_round_matching_relative(
    result: list[dict],
    tfls: list[dict],
    similarity_matrix: np.ndarray,
    used_tfl_indices: set,
    absolute_threshold: float,
    relative_threshold_ratio: float,
    debug: bool = False,
):
    if not (set(range(len(tfls))) - used_tfl_indices):
        if debug:
            app_logger.debug("\n=== 第二轮跳过：无剩余TFL ===")
        return

    if debug:
        app_logger.debug(
            f"\n=== 第二轮匹配开始：绝对阈值 {absolute_threshold:.4f}，相对阈值比例 {relative_threshold_ratio:.2f} ==="
        )

    for i in range(len(result)):
        max_score_for_intext = max(similarity_matrix[i])
        relative_threshold = max_score_for_intext * relative_threshold_ratio
        effective_threshold = max(absolute_threshold, relative_threshold)

        if debug:
            app_logger.debug(
                f"  {result[i]['inTextName']}: 最高分={max_score_for_intext:.4f}, "
                f"相对阈值={relative_threshold:.4f}, 有效阈值={effective_threshold:.4f}"
            )

        candidates_for_intext = [
            (j, similarity_matrix[i][j])
            for j in range(len(tfls))
            if j not in used_tfl_indices and similarity_matrix[i][j] >= effective_threshold
        ]

        if candidates_for_intext:
            candidates_for_intext.sort(key=lambda x: x[1], reverse=True)
            best_tfl_idx, best_score = candidates_for_intext[0]

            result[i]["tfls"].append(tfls[best_tfl_idx])
            used_tfl_indices.add(best_tfl_idx)

            if debug:
                app_logger.debug(
                    f"    匹配: {result[i]['inTextName']} -> {tfls[best_tfl_idx]['name']} (score: {best_score:.4f})"
                )


def _log_matching_results(result: list[dict]):
    app_logger.info("=== 最终匹配结果 ===")
    total_matches = 0
    for entry in result:
        match_count = len(entry["tfls"])
        total_matches += match_count
        tfl_names = [tfl["name"] for tfl in entry["tfls"]]
        app_logger.info(f"  {entry['inTextName']}: {match_count} 个匹配 -> {tfl_names}")
    app_logger.info(f"总计完成 {total_matches} 个匹配")


if __name__ == "__main__":
    in_text_tfls = [
        {"sectionName": "10.1 患者分布", "inTextName": "患者分布"},
        {"sectionName": "10.2 方案偏离", "inTextName": "方案偏离"},
        {"sectionName": "11.1 分析数据集", "inTextName": "分析数据集（FAS）"},
        {"sectionName": "11.2.1 人口统计学", "inTextName": "人口统计学和基线特征"},
    ]

    tfls = [
        {"id": 1, "name": "试验筛选情况（所有筛选患者）", "code": "14.1.1.1"},
        {"id": 2, "name": "患者分布（FAS）", "code": "14.1.1.2"},
        {"id": 3, "name": "重大方案偏离汇总（FAS）", "code": "14.1.2.1"},
        {"id": 4, "name": "方案偏离程度", "code": "14.1.1.3"},
        {"id": 5, "name": "人口统计学和基线特征", "code": "14.1.3.1"},
        {"id": 6, "name": "既往淋巴瘤病史和基线疾病特征", "code": "表14.1.3.2"},
        {"id": 7, "name": "不同剂量组治疗后的最佳总体疗效（FAS）", "code": "表14.2.1.1a"},
        {"id": 8, "name": "各患者（按剂量组分）疗效评价游泳图（FAS）", "code": "图14.2.1.1a"},
        {"id": 9, "name": "不同剂量组的无进展生存期", "code": "表14.2.1.2a"},
        {"id": 10, "name": "不同剂量组的缓解持续时间（FAS）", "code": "表14.2.1.3a"},
        {"id": 11, "name": "既往抗肿瘤治疗史（FAS）", "code": "表14.1.4"},
    ]

    app_logger.debug("=== 测试自定义分词 ===")
    test_texts = [
        "分析数据集（FAS）",
        "11.4.1.1.1 最佳总体疗效",
        "患者分布（ABC）",
        "人口统计学和基线特征",
        "14.1.3.1 FAS 集数据",
        "方案偏离-情况汇总",
    ]

    for text in test_texts:
        tokens = custom_tokenize(text)
        app_logger.debug(f"'{text}' -> {tokens}")

    app_logger.debug("\n" + "=" * 50)

    result = match_tfls(in_text_tfls, tfls, threshold=0.3, relative_threshold_ratio=0.4, debug=True)
