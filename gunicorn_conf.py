# gunicorn_conf.py
# 并行工作进程数
workers = 4
# 指定每个工作者的线程数
threads = 2
# 监听内网端口
bind = "0.0.0.0:8000"

# 设置守护进程,将进程交给supervisor管理
daemon = "false"
# 工作模式协程（明确指定稳定的 worker 类型）
worker_class = "uvicorn.workers.UvicornWorker"
# 设置最大并发量
worker_connections = 1000

# 超时配置（增加启动超时时间）
timeout = 180
graceful_timeout = 30
keepalive = 5

# 进程管理配置
max_requests = 1000
max_requests_jitter = 100
preload_app = False  # 避免预加载导致的启动问题

# 启用端口重用（解决 K8s 容器替换时的端口占用问题）
reuse_port = True

# 设置进程文件目录
pidfile = "/root/logs/gunicorn.pid"
# 设置访问日志和错误信息日志路径
accesslog = "/root/logs/gunicorn_acess.log"
errorlog = "/root/logs/gunicorn_error.log"
# 设置日志记录水平
loglevel = "info"

# 启动配置
worker_tmp_dir = "/dev/shm"  # 使用内存文件系统提高性能
tmp_upload_dir = None
