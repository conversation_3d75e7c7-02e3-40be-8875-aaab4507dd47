#!/usr/bin/env python
# encoding: utf-8
import os

from fastapi import APIRouter, FastAPI
from fastapi.middleware.cors import CORSMiddleware


from aop.context import ContextMiddleware
from api.doc_diff_api import router as doc_diff_router
from api.file_api import router as file_router
from api.file_loader import router as file_loader_router
from api.health import router as health_router
from api.img2md import router as img2md_router
from api.ocr_api import router as ocr_router
from api.pdf_parser import router as parser_router
from api.protocol_api import router as protocol_router
from api.tfl_api import router as tfl_router
from api.tense_api import router as tense_router
from configurer.yy_nacos import init_nacos_manager
from logger.logger import app_logger
import threading

def _setDaemon(self, daemonic: bool):
    self.daemon = daemonic
threading.Thread.setDaemon = _setDaemon

nacos_manager = init_nacos_manager()

def startup_nacos():
    """Nacos 启动初始化函数"""
    try:
        # 检查是否禁用 Nacos
        disable_nacos = os.environ.get("DISABLE_NACOS", "false").lower() == "true"
        if disable_nacos:
            app_logger.info("Nacos 已被禁用，跳过初始化")
            return

        app_logger.info("开始初始化 Nacos 配置")

        # 检查是否已经有配置了（避免重复初始化）
        from configurer.yy_nacos import config

        if config:
            app_logger.info(f"发现已存在的 Nacos 配置，共 {len(config)} 个配置项")
            return
        nacos_manager.init_client()
        app_logger.info("Nacos 配置初始化成功")

    except Exception as e:
        app_logger.error(f"Nacos 配置初始化失败: {e}")
        # 不抛出异常，让应用继续启动
        app_logger.warning("应用将在没有 Nacos 配置的情况下启动")

def shutdown_nacos():
    if nacos_manager:
        nacos_manager.stop()
    print("[NACOS] client closed.")

def create_app():
    _app = FastAPI()

    origins = [
        "*",
    ]

    _app.add_middleware(
        CORSMiddleware,
        allow_origins=origins,
        allow_credentials=True,
        allow_methods=["*"],
        allow_headers=["*"],
    )
    _app.add_middleware(ContextMiddleware)

    # API routers
    router = APIRouter()
    router.include_router(health_router, include_in_schema=False)
    router.include_router(parser_router, include_in_schema=False)
    router.include_router(ocr_router, include_in_schema=False)
    router.include_router(file_router, include_in_schema=False)
    router.include_router(doc_diff_router, include_in_schema=False)
    router.include_router(file_loader_router, include_in_schema=False)
    router.include_router(img2md_router, include_in_schema=False)
    router.include_router(tfl_router, include_in_schema=False)
    router.include_router(protocol_router, include_in_schema=False)
    router.include_router(tense_router, include_in_schema=False)

    _app.include_router(router)

    # 使用 Nacos 启动事件处理器
    _app.add_event_handler("startup", startup_nacos)
    _app.add_event_handler("shutdown", shutdown_nacos)

    app_logger.info("app created.")
    return _app


app = create_app()

if __name__ == "__main__":
    import uvicorn

    uvicorn.run("app:app", host="127.0.0.1", port=8000, reload=True, access_log=False)
